import 'package:flutter/material.dart';
import '../data/subscriber_model.dart';
import '../domain/subscribers_repository.dart';
import '../../../db_helper.dart';
import '../../../auto_notifications_screen.dart' as auto_notify;
import 'dart:convert';
import '../../../boards_list_screen.dart'; // لاستخدام encryptWithOpenSSL
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import '../../../utils/api_helper.dart' as api_helper;
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:crypto/crypto.dart';
import '../data/transaction_model.dart' as MyTrans;
import 'package:url_launcher/url_launcher.dart';

class RenewSubscriptionBottomSheet extends StatefulWidget {
  final Subscriber subscriber;
  final SubscribersRepository repository;
  const RenewSubscriptionBottomSheet({
    super.key,
    required this.subscriber,
    required this.repository,
  });

  @override
  State<RenewSubscriptionBottomSheet> createState() =>
      _RenewSubscriptionBottomSheetState();
}

class _RenewSubscriptionBottomSheetState
    extends State<RenewSubscriptionBottomSheet> {
  bool isLoading = false;
  String? errorMsg;
  bool isPaid = false;
  bool isPaidArrived = true; // متغير لتحديد إذا كان المبلغ واصل
  bool isAmountEditable = false; // للتحكم في تعديل المبلغ المدفوع
  final TextEditingController paidAmountController =
      TextEditingController(); // المبلغ المدفوع فعلياً
  final TextEditingController notesController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController payloadController =
      TextEditingController(); // حقل إدخال البايلود
  List<Map<String, dynamic>> subscriptions = [];
  Map<String, dynamic>? selectedBundle;
  DateTime? startDate;
  final TextEditingController startDateController = TextEditingController();
  String? _decryptedText; // متغير لتخزين النص المفكوك

  @override
  void initState() {
    super.initState();
    _loadSubscriptions();
    startDate = DateTime.now();
    _updateStartDateController();
    isPaidArrived = false; // القيمة الافتراضية دائماً غير مؤشر
    _initBundleFromDB();
  }

  Future<void> _initBundleFromDB() async {
    final subId = widget.subscriber.subscriptionId?.toString();
    if (subId != null && subId.isNotEmpty) {
      final bundle = await DBHelper.instance.getSubscriptionById(subId);
      if (bundle != null) {
        setState(() {
          selectedBundle = bundle;
          priceController.text = bundle['sellPrice']?.toString() ?? '';
        });
        return;
      }
    }
    // احتياطي: استخدم بيانات المشترك إذا لم توجد الباقة في قاعدة البيانات
    setState(() {
      selectedBundle = {
        'id': widget.subscriber.subscriptionId,
        'name': widget.subscriber.subscriptionType,
        'sellPrice': widget.subscriber.subscriptionPrice,
      };
      priceController.text = selectedBundle?['sellPrice']?.toString() ?? '';
    });
  }

  Future<void> _loadSubscriptions() async {
    final list = await DBHelper.instance.getAllSubscriptions();
    setState(() {
      subscriptions = list;
    });
  }

  void _updateStartDateController() {
    if (startDate == null) {
      startDateController.text = '';
    } else {
      final d = startDate!;
      final hour = d.hour % 12 == 0 ? 12 : d.hour % 12;
      final ampm = d.hour >= 12 ? 'PM' : 'AM';
      startDateController.text =
          '${d.year}/${d.month.toString().padLeft(2, '0')}/${d.day.toString().padLeft(2, '0')} ($hour:${d.minute.toString().padLeft(2, '0')} $ampm)';
    }
  }

  Future<void> _pickDate() async {
    final now = DateTime.now();
    final picked = await showDatePicker(
      context: context,
      initialDate: startDate ?? now,
      firstDate: DateTime(now.year - 2),
      lastDate: DateTime(now.year + 2),
      locale: const Locale('ar'),
    );
    if (picked != null) {
      final pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(startDate ?? now),
        builder: (context, child) =>
            Directionality(textDirection: TextDirection.rtl, child: child!),
      );
      setState(() {
        startDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          pickedTime?.hour ?? now.hour,
          pickedTime?.minute ?? now.minute,
        );
        _updateStartDateController();
      });
    }
  }

  @override
  void dispose() {
    notesController.dispose();
    startDateController.dispose();
    priceController.dispose();
    payloadController.dispose(); // التخلص من الكنترولر
    super.dispose();
  }

  /// تجديد المشترك اليدوي محلياً (إضافة 30 يوم)
  Future<void> _renewManualSubscriberLocally() async {
    try {
      print('[RENEW_LOCAL] بدء التجديد المحلي للمشترك اليدوي...');

      // حساب تاريخ الانتهاء الجديد (إضافة 30 يوم)
      final currentEndDate = widget.subscriber.endDate;
      final now = DateTime.now();

      // إذا كان الاشتراك منتهي، نبدأ من اليوم
      // إذا كان ما زال فعال، نضيف 30 يوم على تاريخ الانتهاء الحالي
      final newEndDate = currentEndDate.isAfter(now)
          ? currentEndDate.add(const Duration(days: 30))
          : now.add(const Duration(days: 30));

      print('[RENEW_LOCAL] تاريخ الانتهاء الحالي: $currentEndDate');
      print('[RENEW_LOCAL] تاريخ الانتهاء الجديد: $newEndDate');

      // تحديث سعر الاشتراك إذا تم تغييره
      double subscriptionPrice = widget.subscriber.subscriptionPrice;
      if (priceController.text.isNotEmpty) {
        subscriptionPrice =
            double.tryParse(priceController.text) ?? subscriptionPrice;
      }

      // تحديث نوع الاشتراك إذا تم تغييره
      String subscriptionType = widget.subscriber.subscriptionType;
      if (selectedBundle != null) {
        subscriptionType = selectedBundle!['name'] ?? subscriptionType;
      }

      // تحديث المشترك
      final updatedSubscriber = widget.subscriber.copyWith(
        endDate: newEndDate,
        subscriptionPrice: subscriptionPrice,
        subscriptionType: subscriptionType,
      );

      await widget.repository.updateSubscriber(updatedSubscriber);
      print('[RENEW_LOCAL] تم تحديث المشترك في قاعدة البيانات');

      // إدارة الدفعات والديون
      await _handleLocalRenewalPayment(subscriptionPrice);

      // إضافة سجل عملية التجديد
      await DBHelper.instance.insertTransaction(
        MyTrans.Transaction(
          type: MyTrans.TransactionType.renewal,
          description:
              'تجديد محلي لمشترك يدوي - $subscriptionType بسعر ${subscriptionPrice.toStringAsFixed(0)} د.ع${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
          date: DateTime.now(),
          subscriberId: widget.subscriber.id,
        ),
      );

      print('[RENEW_LOCAL] تم إكمال التجديد المحلي بنجاح');

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تجديد الاشتراك محلياً بنجاح (30 يوم)'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      print('[RENEW_LOCAL][ERROR] خطأ في التجديد المحلي: $e');
      setState(() {
        errorMsg = 'خطأ في التجديد المحلي: $e';
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  /// معالجة الدفعات والديون للتجديد المحلي
  Future<void> _handleLocalRenewalPayment(double subscriptionPrice) async {
    double finalDebt = widget.subscriber.totalDebt;

    if (!isPaidArrived) {
      // إذا لم يتم استلام المبلغ - إضافة مبلغ الباقة كدين
      if (subscriptionPrice > 0) {
        finalDebt += subscriptionPrice;
        print(
          '[RENEW_LOCAL][DEBT] تمت إضافة دين بقيمة $subscriptionPrice. إجمالي الدين: $finalDebt',
        );
      }
    } else {
      // إذا تم استلام المبلغ
      final paidAmount =
          double.tryParse(paidAmountController.text) ?? subscriptionPrice;

      if (paidAmount >= subscriptionPrice) {
        // المبلغ المدفوع يغطي سعر الباقة أو أكثر
        final excess = paidAmount - subscriptionPrice;
        if (excess > 0) {
          // خصم الدين الموجود من المبلغ الزائد
          finalDebt = (finalDebt - excess).clamp(0.0, double.infinity);
          print(
            '[RENEW_LOCAL][PAYMENT] تم دفع $paidAmount، سعر الباقة $subscriptionPrice، خصم من الدين: $excess، الدين المتبقي: $finalDebt',
          );
        }
      } else {
        // المبلغ المدفوع أقل من سعر الباقة
        final shortage = subscriptionPrice - paidAmount;
        finalDebt += shortage;
        print(
          '[RENEW_LOCAL][PAYMENT] تم دفع $paidAmount، سعر الباقة $subscriptionPrice، إضافة للدين: $shortage، إجمالي الدين: $finalDebt',
        );
      }
    }

    // تحديث الدين إذا تغير
    if (finalDebt != widget.subscriber.totalDebt) {
      final updatedSubscriber = widget.subscriber.copyWith(
        totalDebt: finalDebt,
      );
      await widget.repository.updateSubscriber(updatedSubscriber);
      print('[RENEW_LOCAL] تم تحديث الدين إلى: $finalDebt');
    }
  }

  /// ترجع تاريخ الانتهاء الجديد من الموقع أو null عند الفشل
  Future<DateTime?> _renewOnServer({
    required String username,
    int? profileId, // جعل profileId اختياري
    required DateTime startDate,
    required String token,
    required int userId, // إضافة userId
  }) async {
    print('[RENEW] بدء عملية التجديد على الموقع...');

    // جلب اللوحة النشطة للحصول على الدومين
    final board = await DBHelper.instance.getActiveBoard();
    if (board == null ||
        board['url'] == null ||
        board['url'].toString().trim().isEmpty) {
      print('[RENEW][ERROR] لا توجد لوحة نشطة أو الدومين فارغ');
      return null;
    }

    final domain = board['url'].toString().trim();
    print('[RENEW][DEBUG] الدومين المستخدم: $domain');
    print('[RENEW][DEBUG] --- تفاصيل عملية التجديد ---');
    print('[RENEW][DEBUG] user_id: $userId');
    print('[RENEW][DEBUG] username: $username');
    print('[RENEW][DEBUG] profile_id: $profileId');
    print('[RENEW][DEBUG] start_date: ${startDate.toIso8601String()}');
    print('[RENEW][DEBUG] activation_method: Manager Balance');
    // توليد transaction_id تلقائياً
    String transactionId = UniqueKey().toString();
    // بناء البايلود الاحترافي
    final Map<String, dynamic> data = {
      'method': 'credit',
      'pin': '',
      'user_id': userId,
      'money_collected': 1,
      'comments': null,
      'user_price': int.tryParse(priceController.text) ?? 0,
      'issue_invoice': 0,
      'transaction_id': transactionId,
      'activation_units': 1,
      'username': username,
      'start_date': startDate
          .toIso8601String()
          .replaceFirst('T', ' ')
          .split('.')
          .first,
      'activation_method': 'Manager Balance',
    };

    // إضافة profile_id فقط إذا تم تمريره
    if (profileId != null) {
      data['profile_id'] = profileId;
    }
    print('[RENEW][DEBUG] JSON قبل التشفير: ${jsonEncode(data)}');
    final payload = encryptWithOpenSSL(
      jsonEncode(data),
      'abcdefghijuklmno0123456789012345',
    );
    print('[RENEW][DEBUG] payload المشفر: $payload');
    final dio = Dio();
    (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
        (client) {
          client.badCertificateCallback = (cert, host, port) {
            print('[RENEW][SSL] تجاوز تحقق الشهادة لـ $host:$port');
            return true;
          };
          return client;
        };
    // بناء الرابط الديناميكي
    final apiUrl = domain.startsWith('http')
        ? '$domain/admin/api/index.php/api/user/activate'
        : 'https://$domain/admin/api/index.php/api/user/activate';

    final headers = {
      'authorization': 'Bearer $token',
      'content-type': 'application/json',
      'accept': 'application/json, text/plain, */*',
      'origin': domain.startsWith('http') ? domain : 'https://$domain',
      'referer': domain.startsWith('http') ? '$domain/' : 'https://$domain/',
      'user-agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'accept-language': 'en,en-US;q=0.9,ar;q=0.8',
      'host': domain.replaceAll(RegExp(r'https?://'), ''),
      'sec-ch-ua':
          '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
    };
    print('[RENEW][DEBUG] الرابط المستخدم: $apiUrl');
    print('[RENEW][DEBUG] headers المرسلة: ${headers.toString()}');
    final response = await dio.post(
      apiUrl,
      data: jsonEncode({'payload': payload}),
      options: Options(headers: headers, validateStatus: (status) => true),
    );
    print('[RENEW][DEBUG] كود الاستجابة: ${response.statusCode}');
    print('[RENEW][DEBUG] محتوى الرد: ${response.data}');
    if (response.statusCode == 200) {
      if (response.data == null ||
          (response.data is String && response.data.trim().isEmpty)) {
        print('[RENEW][ERROR] الاستجابة من السيرفر فارغة!');
        return null;
      }
      final resp = response.data is String
          ? jsonDecode(response.data)
          : response.data;
      print('[RENEW] الاستجابة المفككة: $resp');
      // محاولة استخراج تاريخ الانتهاء الجديد من الرد
      if (resp is Map<String, dynamic>) {
        final endDateStr =
            resp['end_date'] ?? resp['expire_date'] ?? resp['expireDate'];
        if (endDateStr != null &&
            endDateStr is String &&
            endDateStr.isNotEmpty) {
          try {
            final endDate = DateTime.parse(endDateStr);
            return endDate;
          } catch (e) {
            print('[RENEW][ERROR] فشل تحويل تاريخ الانتهاء: $e');
          }
        }
      }
      return null;
    } else {
      print('[RENEW][ERROR] كود استجابة غير متوقع: ${response.statusCode}');
    }
    return null;
  }

  Future<String?> _getValidToken() async {
    // جلب بيانات اللوحة النشطة
    final board = await DBHelper.instance.getActiveBoard();
    if (board == null) return null;
    String? token = board['token'];
    if (token == null || token.isEmpty) {
      // تسجيل الدخول وجلب التوكن
      final loginResult = await api_helper.tryConnect(board);
      if (loginResult['success'] == true && loginResult['token'] != null) {
        token = loginResult['token'];
        // تحديث التوكن في قاعدة البيانات
        await DBHelper.instance.updateBoard(board['id'], {
          ...board,
          'token': token,
        });
      }
    }
    return token;
  }

  Future<void> _renew() async {
    setState(() {
      isLoading = true;
      errorMsg = null;
    });
    try {
      print('[RENEW] بدء منطق التجديد...');

      // فحص نوع المشترك أولاً
      if (widget.subscriber.isManualSubscriber) {
        print('[RENEW] مشترك يدوي - تنفيذ التجديد المحلي...');
        await _renewManualSubscriberLocally();
        return;
      }

      // للمشتركين الآخرين (SAS) - التجديد عبر السيرفر
      print('[RENEW] مشترك SAS - بدء منطق التجديد عبر السيرفر...');
      final token = await _getValidToken() ?? '';
      print('[RENEW] التوكن المستخدم: $token');
      if (token.isEmpty) {
        setState(() {
          errorMsg = 'لم يتم العثور على توكن صالح. تأكد من بيانات الدخول.';
        });
        print('[RENEW][ERROR] لم يتم العثور على توكن صالح.');
        return;
      }
      final oldProfileId = widget.subscriber.subscriptionId?.toString() ?? '';
      final newProfileId = selectedBundle?['id']?.toString() ?? '';
      final isProfileChanged =
          oldProfileId != newProfileId && newProfileId.isNotEmpty;
      final isExpired = widget.subscriber.endDate.isBefore(DateTime.now());
      print(
        '[RENEW] isProfileChanged: $isProfileChanged, isExpired: $isExpired',
      );
      // منطق تغيير الباقة
      if (isProfileChanged) {
        if (!isExpired) {
          setState(() {
            errorMsg =
                'لا يمكن تغيير نوع الباقة إلا بعد انتهاء الاشتراك الحالي.';
          });
          print('[RENEW][ERROR] محاولة تغيير باقة لمشترك فعال');
          return;
        }
        // إذا كان الاشتراك منتهي، نفذ طلب تغيير الباقة أولاً
        // جلب بيانات اللوحة النشطة
        final board = await DBHelper.instance.getActiveBoard();
        if (board == null) {
          setState(() {
            errorMsg = 'لم يتم العثور على بيانات اللوحة النشطة.';
          });
          print('[RENEW][ERROR] لم يتم العثور على بيانات اللوحة النشطة.');
          return;
        }
        // إذا كان الاشتراك منتهي، نفذ طلب تغيير الباقة أولاً
        final changeResult = await api_helper.changeProfileOnServer(
          board: board,
          username: widget.subscriber.user,
          profileId: int.tryParse(newProfileId) ?? 0,
          userId:
              widget.subscriber.remoteId ?? 0, // تمرير رقم المشترك من السيرفر
        );
        if (!(changeResult['success'] ?? false)) {
          setState(() {
            errorMsg =
                changeResult['message'] ?? 'فشل تغيير الباقة على السيرفر.';
          });
          print(
            '[RENEW][ERROR] فشل تغيير الباقة على السيرفر: \\${changeResult['message']}',
          );
          return;
        }
        print('[RENEW] تم تغيير الباقة بنجاح، جاري التجديد...');
      }
      // تنفيذ طلب التجديد دائماً إذا لم يكن هناك تغيير باقة أو إذا تم تغييرها بنجاح
      // لا نرسل profileId - السيرفر يستخدم الباقة الحالية (أو الجديدة إذا تم تغييرها)
      final newEndDate = await _renewOnServer(
        username: widget.subscriber.user,
        startDate: startDate ?? DateTime.now(),
        token: token,
        userId: widget.subscriber.remoteId ?? 0, // تمرير رقم المشترك
      );
      // تحديث تاريخ الانتهاء إذا تم جلبه من السيرفر
      DateTime? finalEndDate = widget.subscriber.endDate;
      if (newEndDate != null) {
        final updated = widget.subscriber.copyWith(endDate: newEndDate);
        await widget.repository.updateSubscriber(updated);
        finalEndDate = newEndDate;
        print('[RENEW] تم تحديث تاريخ الانتهاء الجديد: $newEndDate');
      }
      // مزامنة بيانات المشترك من السيرفر بعد نجاح التجديد (اختياري)
      final remoteData = await api_helper.fetchSubscriber(
        widget.subscriber.user,
      );
      if (remoteData != null) {
        await DBHelper.instance.insertOrUpdateSubscriber(remoteData);
        // بعد مزامنة البيانات، أعد تحميل بيانات المشترك من قاعدة البيانات المحلية
        if (widget.subscriber.id != null) {
          final localSub = await DBHelper.instance.getSubscriberById(
            widget.subscriber.id!,
          );
          if (localSub != null) {
            try {
              final syncedEndDate = localSub.endDate;
              finalEndDate = syncedEndDate;
            } catch (_) {}
          }
        }
        print(
          '[RENEW] تمت مزامنة وتحديث قاعدة البيانات وقراءة التاريخ الجديد.',
        );
      } else {
        print(
          '[RENEW][WARNING] لم يتم جلب بيانات المشترك من السيرفر بعد التجديد.',
        );
      }

      // *** إضافة مزامنة شاملة بعد التجديد ***
      DateTime? syncedEndDateAfterSync;
      try {
        final board = await DBHelper.instance.getActiveBoard();
        if (board != null) {
          await api_helper.syncAllFromServer(board: board, silent: true);
          print(
            '[RENEW][SYNC] تمت مزامنة جميع البيانات من السيرفر بعد التجديد.',
          );
          // جلب بيانات المشترك بعد المزامنة الشاملة
          if (widget.subscriber.id != null) {
            final localSub = await DBHelper.instance.getSubscriberById(
              widget.subscriber.id!,
            );
            if (localSub != null) {
              syncedEndDateAfterSync = localSub.endDate;
            }
          }
        }
      } catch (e) {
        print('[RENEW][SYNC][ERROR] فشل مزامنة جميع البيانات بعد التجديد: $e');
      }

      // إضافة سجل عملية التجديد
      await DBHelper.instance.insertTransaction(
        MyTrans.Transaction(
          type: MyTrans.TransactionType.renewal,
          description:
              'تجديد اشتراك ${selectedBundle?['name'] ?? 'باقة'} بسعر ${priceController.text} د.ع${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
          date: DateTime.now(),
          subscriberId: widget.subscriber.id,
        ),
      );

      // منطق إدارة الدفعات والديون المتقدم
      double finalDebt = widget.subscriber.totalDebt;
      double walletBalance = _extractWalletBalance(
        widget.subscriber.notes ?? '',
      );

      if (!isPaidArrived) {
        // إذا لم يتم استلام المبلغ - إضافة مبلغ الباقة كدين
        final subscriptionPrice = double.tryParse(priceController.text) ?? 0.0;
        if (subscriptionPrice > 0) {
          finalDebt += subscriptionPrice;
          print(
            '[RENEW][DEBT] تمت إضافة دين بقيمة $subscriptionPrice. إجمالي الدين: $finalDebt',
          );

          // تحديث المشترك مع الدين الجديد
          final updated = widget.subscriber.copyWith(totalDebt: finalDebt);
          await widget.repository.updateSubscriber(updated);

          // إضافة سجل في جدول المعاملات
          await DBHelper.instance.insertTransaction(
            MyTrans.Transaction(
              type: MyTrans.TransactionType.addDebt,
              description:
                  'إضافة دين عند التجديد ${subscriptionPrice.toStringAsFixed(0)}${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
              date: DateTime.now(),
              subscriberId: widget.subscriber.id,
            ),
          );
        }
      } else {
        // إذا تم استلام المبلغ - معالجة الدفعة المتقدمة
        final paidAmount = double.tryParse(paidAmountController.text) ?? 0.0;
        final subscriptionPrice = double.tryParse(priceController.text) ?? 0.0;

        if (paidAmount > 0 && subscriptionPrice > 0) {
          print(
            '[RENEW][PAYMENT] بدء معالجة الدفعة: مدفوع=$paidAmount، سعر الباقة=$subscriptionPrice، ديون حالية=$finalDebt',
          );

          if (paidAmount >= subscriptionPrice) {
            // الحالة 1: المبلغ المدفوع يكفي أو يزيد عن سعر الباقة
            double remainingAmount = paidAmount - subscriptionPrice;
            print(
              '[RENEW][PAYMENT] المبلغ يكفي للاشتراك. المبلغ المتبقي: $remainingAmount',
            );

            if (finalDebt > 0 && remainingAmount > 0) {
              // إذا كان هناك ديون سابقة ومبلغ متبقي، ادفع من الديون
              if (remainingAmount >= finalDebt) {
                // المبلغ يكفي لسداد كل الديون
                remainingAmount -= finalDebt;
                print('[RENEW][PAYMENT] تم سداد كامل الديون بقيمة $finalDebt');
                finalDebt = 0;
              } else {
                // المبلغ يكفي لسداد جزء من الديون
                finalDebt -= remainingAmount;
                print(
                  '[RENEW][PAYMENT] تم سداد جزء من الديون بقيمة $remainingAmount. الدين المتبقي: $finalDebt',
                );
                remainingAmount = 0;
              }
            }

            if (remainingAmount > 0) {
              // إضافة المبلغ المتبقي للمحفظة
              walletBalance += remainingAmount;
              print(
                '[RENEW][WALLET] تمت إضافة $remainingAmount للمحفظة. الرصيد الجديد: $walletBalance',
              );
            }
          } else {
            // الحالة 2: المبلغ المدفوع أقل من سعر الباقة (دفع جزئي)
            double shortfall = subscriptionPrice - paidAmount;
            print('[RENEW][PAYMENT] دفع جزئي. النقص: $shortfall');

            if (finalDebt > 0) {
              // إذا كان هناك ديون سابقة، أضف النقص للديون الموجودة
              finalDebt += shortfall;
              print(
                '[RENEW][DEBT] تمت إضافة النقص للديون الموجودة. إجمالي الدين: $finalDebt',
              );
            } else {
              // إذا لم تكن هناك ديون سابقة، أنشئ دين جديد للنقص
              finalDebt = shortfall;
              print('[RENEW][DEBT] تم إنشاء دين جديد للنقص: $finalDebt');
            }

            // لا يتم إضافة شيء للمحفظة في حالة الدفع الجزئي
          }

          // تحديث notes مع رصيد المحفظة الجديد
          String updatedNotes = _updateNotesWithWallet(
            widget.subscriber.notes ?? '',
            walletBalance,
          );

          // تحديث بيانات المشترك
          final updated = widget.subscriber.copyWith(
            totalDebt: finalDebt,
            notes: updatedNotes.isEmpty ? null : updatedNotes,
          );
          await widget.repository.updateSubscriber(updated);

          // إضافة سجل المعاملة المناسب
          if (paidAmount >= subscriptionPrice) {
            // سجل دفعة كاملة أو زائدة
            await DBHelper.instance.insertTransaction(
              MyTrans.Transaction(
                type: MyTrans.TransactionType.addCredit,
                description:
                    'دفعة عند التجديد ${paidAmount.toStringAsFixed(0)}${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
                date: DateTime.now(),
                subscriberId: widget.subscriber.id,
              ),
            );
          } else {
            // سجل دفعة جزئية + دين للنقص
            await DBHelper.instance.insertTransaction(
              MyTrans.Transaction(
                type: MyTrans.TransactionType.addCredit,
                description:
                    'دفعة جزئية عند التجديد ${paidAmount.toStringAsFixed(0)}${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
                date: DateTime.now(),
                subscriberId: widget.subscriber.id,
              ),
            );

            // سجل الدين للنقص
            await DBHelper.instance.insertTransaction(
              MyTrans.Transaction(
                type: MyTrans.TransactionType.addDebt,
                description:
                    'دين نقص الدفعة عند التجديد ${(subscriptionPrice - paidAmount).toStringAsFixed(0)}',
                date: DateTime.now(),
                subscriberId: widget.subscriber.id,
              ),
            );
          }

          print(
            '[RENEW][FINAL] النتيجة النهائية - الدين: $finalDebt، المحفظة: $walletBalance',
          );
        }
      }
      // إرسال رسالة واتساب بعد التجديد باستخدام تاريخ الانتهاء الجديد أو المزامن أو بعد المزامنة الشاملة
      try {
        final renewMsg = await DBHelper.instance.getMessage(
          'start_msg',
          'مرحباً {الاسم}، تم تجديد اشتراكك بنجاح. نوع الاشتراك: {نوع_الاشتراك}، السعر: {سعر_الاشتراك}، الدين الحالي: {الدين}، تاريخ البدء: {تاريخ_البدء}، تاريخ الانتهاء: {تاريخ_الانتهاء}. شكراً لاختيارك لنا.',
        );
        String phone = widget.subscriber.phone.trim();
        if (phone.startsWith('0')) {
          phone = '964${phone.substring(1)}';
        }
        phone = phone.replaceAll(RegExp(r'[^0-9]'), '');
        // استخدم تاريخ الانتهاء بعد المزامنة الشاملة إذا توفر، وإلا fallback للمنطق السابق
        DateTime endDateForMsg =
            syncedEndDateAfterSync ?? finalEndDate ?? widget.subscriber.endDate;
        // لا ترفق startDate إلا إذا كان القالب يحتوي عليه، وبتنسيق التاريخ مع الوقت إذا لزم
        String? startDateStr;
        if (renewMsg.contains('{تاريخ_البدء}')) {
          final d = startDate ?? DateTime.now();
          // استخدم نفس منطق تنسيق تاريخ الانتهاء
          startDateStr = await auto_notify.formatDateForMessage(d);
        }
        String msg = await auto_notify.fillMessageVars(
          renewMsg,
          name: widget.subscriber.name,
          phone: phone,
          type:
              selectedBundle?['name']?.toString() ??
              widget.subscriber.subscriptionType,
          price: priceController.text,
          debt: finalDebt.toString(),
          startDate: startDateStr,
          endDate: endDateForMsg,
        );
        final whatsappUrl = Uri.parse(
          'https://wa.me/$phone?text=${Uri.encodeComponent(msg)}',
        );

        try {
          final launched = await launchUrl(
            whatsappUrl,
            mode: LaunchMode.externalApplication,
          );

          if (launched) {
            debugPrint('[RENEW][WHATSAPP] تم فتح واتساب بنجاح');
          } else {
            debugPrint(
              '[RENEW][WHATSAPP] فشل فتح واتساب - launchUrl returned false',
            );
          }
        } catch (e) {
          debugPrint('[RENEW][WHATSAPP] فشل إرسال رسالة واتساب: $e');

          // محاولة ثانية بالبروتوكول المباشر
          try {
            final directUri = Uri.parse(
              'whatsapp://send?phone=$phone&text=${Uri.encodeComponent(msg)}',
            );
            final launched = await launchUrl(
              directUri,
              mode: LaunchMode.externalApplication,
            );

            if (launched) {
              debugPrint(
                '[RENEW][WHATSAPP] تم فتح واتساب بنجاح عبر البروتوكول المباشر',
              );
            }
          } catch (e2) {
            debugPrint(
              '[RENEW][WHATSAPP] فشل فتح واتساب عبر البروتوكول المباشر: $e2',
            );
          }
        }
      } catch (e) {
        debugPrint('[RENEW][WHATSAPP] خطأ عام في إرسال واتساب: $e');
      }
      if (mounted) {
        Navigator.of(context).pop(true); // فقط إغلاق الشاشة مع إرجاع true
      }
    } catch (e) {
      setState(() {
        errorMsg = 'حدث خطأ أثناء التجديد';
      });
      debugPrint('[RENEW][ERROR] استثناء أثناء التجديد: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
      debugPrint('[RENEW] انتهاء منطق التجديد.');
    }
  }

  // دالة فك تشفير متوافقة مع OpenSSL/CryptoJS AES CBC PKCS7
  String decryptWithOpenSSL(String encryptedBase64, String passphrase) {
    final encrypted = base64.decode(encryptedBase64);
    if (utf8.decode(encrypted.sublist(0, 8)) != 'Salted__') {
      throw Exception('Missing Salted__ prefix');
    }
    final salt = encrypted.sublist(8, 16);
    final keyAndIV = _evpBytesToKey(passphrase.codeUnits, salt, 32, 16);
    final key = keyAndIV.sublist(0, 32);
    final iv = Uint8List.fromList(keyAndIV.sublist(32, 48));
    final cipher = encrypt.Encrypter(
      encrypt.AES(
        encrypt.Key(Uint8List.fromList(key)),
        mode: encrypt.AESMode.cbc,
        padding: null,
      ),
    );
    final decrypted = cipher.decryptBytes(
      encrypt.Encrypted(encrypted.sublist(16)),
      iv: encrypt.IV(iv),
    );
    // إزالة PKCS7 padding
    final pad = decrypted.last;
    final unpadded = decrypted.sublist(0, decrypted.length - pad);
    return utf8.decode(unpadded);
  }

  // دالة evpBytesToKey المطلوبة لفك التشفير (نفس دالة التشفير)
  List<int> _evpBytesToKey(
    List<int> password,
    List<int> salt,
    int keyLen,
    int ivLen,
  ) {
    final totalLen = keyLen + ivLen;
    var derived = <int>[];
    var block = <int>[];
    while (derived.length < totalLen) {
      final hasher = md5.convert([...block, ...password, ...salt]);
      block = hasher.bytes;
      derived.addAll(block);
    }
    return derived.sublist(0, totalLen);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // رأس الشاشة العصري
          _buildModernHeader(colorScheme),

          // المحتوى القابل للتمرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // بطاقة معلومات المشترك
                  _buildSubscriberInfoCard(colorScheme),
                  const SizedBox(height: 20),

                  // بطاقة اختيار الباقة والسعر
                  _buildPackageSelectionCard(colorScheme),
                  const SizedBox(height: 20),

                  // بطاقة الإعدادات الإضافية
                  _buildAdditionalSettingsCard(colorScheme),
                  const SizedBox(height: 20),

                  // رسالة توضيحية للمشتركين اليدويين
                  if (widget.subscriber.isManualSubscriber)
                    _buildManualSubscriberNotice(colorScheme),

                  const SizedBox(height: 20),

                  // زر التجديد العصري
                  _buildRenewButton(colorScheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر نوع المشترك
  Widget _buildSubscriberTypeChip(ColorScheme colorScheme) {
    String typeText;
    Color typeColor;
    IconData typeIcon;

    if (widget.subscriber.isManualSubscriber) {
      typeText = 'يدوي';
      typeColor = Colors.blue;
      typeIcon = Icons.person_add;
    } else if (widget.subscriber.isSasSubscriber) {
      typeText = 'SAS';
      typeColor = Colors.green;
      typeIcon = Icons.cloud;
    } else if (widget.subscriber.isEarthlinkSubscriber) {
      typeText = 'Earthlink';
      typeColor = Colors.orange;
      typeIcon = Icons.language;
    } else {
      typeText = 'SAS'; // افتراضي للمشتركين القدامى
      typeColor = Colors.green;
      typeIcon = Icons.cloud;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: typeColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(typeIcon, size: 12, color: typeColor),
          const SizedBox(width: 4),
          Text(
            typeText,
            style: TextStyle(
              color: typeColor,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  // رأس الشاشة العصري
  Widget _buildModernHeader(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primary.withValues(alpha: 0.1),
            colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // مؤشر السحب
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: colorScheme.outline.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          // أيقونة وعنوان
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.refresh_rounded,
                  color: colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تجديد الاشتراك',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          widget.subscriber.name,
                          style: TextStyle(
                            fontSize: 16,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // مؤشر نوع المشترك
                        _buildSubscriberTypeChip(colorScheme),
                      ],
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(
                  Icons.close_rounded,
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بطاقة معلومات المشترك
  Widget _buildSubscriberInfoCard(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person_rounded,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات المشترك',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('الاسم', widget.subscriber.name, colorScheme),
            _buildInfoRow('اسم المستخدم', widget.subscriber.user, colorScheme),
            _buildInfoRow(
              'الباقة الحالية',
              widget.subscriber.subscriptionType,
              colorScheme,
            ),
            _buildInfoRow(
              'تاريخ الانتهاء',
              '${widget.subscriber.endDate.day}/${widget.subscriber.endDate.month}/${widget.subscriber.endDate.year}',
              colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  // صف معلومات
  Widget _buildInfoRow(String label, String value, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بطاقة اختيار الباقة والسعر
  Widget _buildPackageSelectionCard(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.inventory_2_rounded,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل التجديد',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // حقل اختيار الباقة والسعر
            TextFormField(
              controller: priceController,
              decoration: InputDecoration(
                labelText: selectedBundle?['name'] != null
                    ? 'سعر الباقة (${selectedBundle?['name']})'
                    : 'سعر الباقة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: Icon(
                  Icons.attach_money_rounded,
                  color: colorScheme.primary,
                ),
                suffixText: 'د.ع',
              ),
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              onTap: subscriptions.isEmpty ? null : _showPackageSelector,
            ),
            const SizedBox(height: 16),

            // حقل المبلغ المدفوع (يظهر فقط عند تفعيل "تم الاستلام")
            if (isPaidArrived) ...[
              TextFormField(
                controller: paidAmountController,
                decoration: InputDecoration(
                  labelText: 'المبلغ المدفوع فعلياً',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: Icon(
                    Icons.payments_rounded,
                    color: colorScheme.primary,
                  ),
                  suffixText: 'د.ع',
                  helperText: 'أدخل المبلغ الذي تم دفعه فعلياً',
                ),
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                readOnly: !isAmountEditable,
                onTap: !isAmountEditable
                    ? () {
                        setState(() {
                          isAmountEditable = true;
                          // تعيين القيمة الافتراضية لسعر الباقة
                          if (paidAmountController.text.isEmpty) {
                            paidAmountController.text = priceController.text;
                          }
                        });
                      }
                    : null,
                onChanged: (value) {
                  // يمكن إضافة منطق تحديث فوري هنا إذا لزم الأمر
                },
              ),
              const SizedBox(height: 8),
              // مؤشر توضيحي للمبلغ
              if (paidAmountController.text.isNotEmpty &&
                  priceController.text.isNotEmpty)
                _buildPaymentSummary(colorScheme),
              const SizedBox(height: 16),
            ],

            // مؤشر حالة الدفع
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isPaidArrived
                    ? colorScheme.primary.withValues(alpha: 0.1)
                    : colorScheme.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isPaidArrived
                      ? colorScheme.primary.withValues(alpha: 0.3)
                      : colorScheme.error.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isPaidArrived
                        ? Icons.check_circle_rounded
                        : Icons.pending_rounded,
                    color: isPaidArrived
                        ? colorScheme.primary
                        : colorScheme.error,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'حالة الدفع',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          isPaidArrived
                              ? 'تم استلام المبلغ'
                              : 'لم يتم استلام المبلغ',
                          style: TextStyle(
                            fontSize: 12,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: isPaidArrived,
                    onChanged: (val) {
                      setState(() {
                        isPaidArrived = val;
                        // إعادة تعيين القيم عند تغيير حالة الدفع
                        if (val) {
                          // إذا تم تفعيل "تم الاستلام"، تعيين المبلغ المدفوع لسعر الباقة
                          paidAmountController.text = priceController.text;
                          isAmountEditable = false;
                        } else {
                          // إذا تم إلغاء "تم الاستلام"، مسح المبلغ المدفوع
                          paidAmountController.clear();
                          isAmountEditable = false;
                        }
                      });
                    },
                    activeColor: colorScheme.primary,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // عرض منتقي الباقات
  Future<void> _showPackageSelector() async {
    final selected = await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (ctx) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'اختر باقة الاشتراك',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
                color: Theme.of(ctx).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: subscriptions.length,
                separatorBuilder: (_, index) => const SizedBox(height: 8),
                itemBuilder: (ctx, idx) {
                  final sub = subscriptions[idx];
                  final isSelected = selectedBundle?['id'] == sub['id'];
                  return Card(
                    elevation: 0,
                    color: isSelected
                        ? Theme.of(
                            ctx,
                          ).colorScheme.primary.withValues(alpha: 0.1)
                        : Theme.of(ctx).colorScheme.surface,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: isSelected
                            ? Theme.of(ctx).colorScheme.primary
                            : Theme.of(
                                ctx,
                              ).colorScheme.outline.withValues(alpha: 0.2),
                      ),
                    ),
                    child: ListTile(
                      leading: Icon(
                        Icons.star_rounded,
                        color: isSelected
                            ? Theme.of(ctx).colorScheme.primary
                            : Colors.amber,
                      ),
                      title: Text(
                        sub['name'] ?? '',
                        style: TextStyle(
                          fontWeight: isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                      subtitle: Text(
                        sub['sellPrice'] != null
                            ? '${sub['sellPrice']} د.ع'
                            : '',
                        style: TextStyle(
                          color: Theme.of(ctx).colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check_circle_rounded,
                              color: Theme.of(ctx).colorScheme.primary,
                            )
                          : null,
                      onTap: () => Navigator.of(ctx).pop(sub),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );

    if (selected != null) {
      setState(() {
        selectedBundle = selected;
        priceController.text = selected['sellPrice']?.toString() ?? '';
      });
    }
  }

  // بطاقة الإعدادات الإضافية
  Widget _buildAdditionalSettingsCard(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings_rounded,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'إعدادات إضافية',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // حقل تاريخ البدء
            TextFormField(
              controller: startDateController,
              decoration: InputDecoration(
                labelText: 'تاريخ البدء',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: Icon(
                  Icons.calendar_today_rounded,
                  color: colorScheme.primary,
                ),
              ),
              readOnly: true,
              onTap: _pickDate,
            ),
            const SizedBox(height: 16),

            // حقل الملاحظات
            TextFormField(
              controller: notesController,
              decoration: InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: Icon(
                  Icons.note_rounded,
                  color: colorScheme.primary,
                ),
              ),
              maxLines: 3,
              minLines: 1,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رسالة توضيحية للمشتركين اليدويين
  Widget _buildManualSubscriberNotice(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.blue, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تجديد محلي',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'سيتم تجديد هذا المشترك محلياً (إضافة 30 يوم) بدون الحاجة للاتصال بالسيرفر',
                  style: TextStyle(
                    color: Colors.blue.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // زر التجديد العصري
  Widget _buildRenewButton(ColorScheme colorScheme) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary,
            colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: isLoading ? null : _renew,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: isLoading
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    colorScheme.onPrimary,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.refresh_rounded,
                    color: colorScheme.onPrimary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.subscriber.isManualSubscriber
                        ? 'تجديد محلي (30 يوم)'
                        : 'تجديد الاشتراك',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  // استخراج رصيد المحفظة من notes
  double _extractWalletBalance(String notes) {
    final walletMatch = RegExp(r'wallet:(\d+(?:\.\d+)?)').firstMatch(notes);
    return walletMatch != null
        ? double.tryParse(walletMatch.group(1)!) ?? 0.0
        : 0.0;
  }

  // تحديث notes مع رصيد المحفظة الجديد
  String _updateNotesWithWallet(String notes, double walletBalance) {
    // إزالة أي wallet موجود في notes
    final cleanNotes = notes
        .replaceAll(RegExp(r'wallet:\d+(?:\.\d+)?'), '')
        .trim();

    // إذا كان رصيد المحفظة أكبر من 0، أضفه في البداية
    if (walletBalance > 0) {
      final walletText = 'wallet:${walletBalance.toStringAsFixed(0)}';
      if (cleanNotes.isNotEmpty) {
        return '$walletText $cleanNotes';
      } else {
        return walletText;
      }
    }

    // إذا لم يكن هناك رصيد محفظة، أرجع notes بدون wallet
    return cleanNotes;
  }

  // ملخص الدفعة
  Widget _buildPaymentSummary(ColorScheme colorScheme) {
    final paidAmount = double.tryParse(paidAmountController.text) ?? 0.0;
    final subscriptionPrice = double.tryParse(priceController.text) ?? 0.0;
    final currentDebt = widget.subscriber.totalDebt;
    final currentWallet = _extractWalletBalance(widget.subscriber.notes ?? '');

    if (paidAmount <= 0 || subscriptionPrice <= 0) {
      return const SizedBox.shrink();
    }

    // حساب التوزيع المتقدم
    double remainingAmount = paidAmount - subscriptionPrice;
    double debtPayment = 0;
    double walletAddition = 0;
    double newDebt = 0;

    if (paidAmount >= subscriptionPrice) {
      // الحالة 1: المبلغ يكفي أو يزيد عن سعر الباقة
      if (currentDebt > 0 && remainingAmount > 0) {
        if (remainingAmount >= currentDebt) {
          debtPayment = currentDebt;
          walletAddition = remainingAmount - currentDebt;
        } else {
          debtPayment = remainingAmount;
        }
      } else if (remainingAmount > 0) {
        walletAddition = remainingAmount;
      }
    } else {
      // الحالة 2: المبلغ أقل من سعر الباقة (دفع جزئي)
      double shortfall = subscriptionPrice - paidAmount;
      newDebt = shortfall;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate_rounded,
                color: colorScheme.primary,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'ملخص الدفعة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildSummaryRow(
            'المبلغ المدفوع:',
            '${paidAmount.toStringAsFixed(0)} د.ع',
            colorScheme,
          ),
          _buildSummaryRow(
            'سعر الاشتراك:',
            '${subscriptionPrice.toStringAsFixed(0)} د.ع',
            colorScheme,
          ),
          if (paidAmount < subscriptionPrice)
            _buildSummaryRow(
              'نقص في الدفعة:',
              '${(subscriptionPrice - paidAmount).toStringAsFixed(0)} د.ع',
              colorScheme,
              color: Colors.red,
            ),
          if (debtPayment > 0)
            _buildSummaryRow(
              'سداد ديون:',
              '${debtPayment.toStringAsFixed(0)} د.ع',
              colorScheme,
              color: Colors.orange,
            ),
          if (walletAddition > 0)
            _buildSummaryRow(
              'إضافة للمحفظة:',
              '${walletAddition.toStringAsFixed(0)} د.ع',
              colorScheme,
              color: Colors.green,
            ),
          const Divider(height: 16),
          _buildSummaryRow(
            'الدين بعد التجديد:',
            '${(currentDebt - debtPayment + newDebt).toStringAsFixed(0)} د.ع',
            colorScheme,
            isBold: true,
            color: (currentDebt - debtPayment + newDebt) > 0
                ? Colors.red
                : Colors.green,
          ),
          _buildSummaryRow(
            'رصيد المحفظة الجديد:',
            '${(currentWallet + walletAddition).toStringAsFixed(0)} د.ع',
            colorScheme,
            isBold: true,
          ),
        ],
      ),
    );
  }

  // صف في ملخص الدفعة
  Widget _buildSummaryRow(
    String label,
    String value,
    ColorScheme colorScheme, {
    Color? color,
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: colorScheme.onSurface.withValues(alpha: 0.8),
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 13,
              color: color ?? colorScheme.onSurface,
              fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
