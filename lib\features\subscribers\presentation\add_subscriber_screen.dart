import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import '../../../utils/api_helper.dart';

import '../../../db_helper.dart';
import '../data/subscriber_model.dart';
import '../domain/subscribers_repository.dart';
import '../../../utils/api_helper.dart' as api_helper;

class AddSubscriberScreen extends StatefulWidget {
  final SubscribersRepository repository;
  const AddSubscriberScreen({super.key, required this.repository});

  @override
  State<AddSubscriberScreen> createState() => _AddSubscriberScreenState();
}

class _AddSubscriberScreenState extends State<AddSubscriberScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _userController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _debtController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _priceFocusNode = FocusNode();
  bool _priceInitialized = false;
  bool _isContinuous = false;
  List<Map<String, dynamic>> _subscriptions = [];
  String? _selectedBundleName;

  // نوع المشترك: محلي أم في السيرفر
  bool _isServerSubscriber = false;
  bool _isLoading = false;

  /// الحصول على سعر البيع الفعال (مخصص أو افتراضي)
  double _getEffectiveSellPrice(Map<String, dynamic> subscription) {
    final customPrice = subscription['custom_sell_price'];
    if (customPrice != null && customPrice > 0) {
      final price = (customPrice is num)
          ? customPrice.toDouble()
          : double.tryParse(customPrice.toString()) ?? 0.0;
      debugPrint(
        '[PRICE] استخدام سعر مخصص: $price للباقة: ${subscription['name']}',
      );
      return price;
    }

    final sellPrice = subscription['sellPrice'];
    if (sellPrice != null) {
      final price = (sellPrice is num)
          ? sellPrice.toDouble()
          : double.tryParse(sellPrice.toString()) ?? 0.0;
      debugPrint(
        '[PRICE] استخدام سعر افتراضي: $price للباقة: ${subscription['name']}',
      );
      return price;
    }

    debugPrint('[PRICE] لا يوجد سعر للباقة: ${subscription['name']}');
    return 0.0;
  }

  Future<void> _loadSubscriptions() async {
    final list = await DBHelper.instance.getAllSubscriptions();
    setState(() {
      _subscriptions = list;
    });
  }

  /// دالة لحساب تاريخ انتهاء الاشتراك بناءً على نوع المدة (احترافية)
  DateTime calculateSubscriptionEndDate(DateTime startDate, int durationType) {
    if (durationType == 0) {
      int year = startDate.year;
      int month = startDate.month + 1;
      if (month > 12) {
        month = 1;
        year += 1;
      }
      int day = startDate.day;
      int lastDayOfNextMonth = DateTime(year, month + 1, 0).day;
      if (day > lastDayOfNextMonth) day = lastDayOfNextMonth;
      return DateTime(
        year,
        month,
        day,
        startDate.hour,
        startDate.minute,
        startDate.second,
      );
    } else {
      return startDate.add(const Duration(days: 30));
    }
  }

  void _save() async {
    // التحقق من الحقول المطلوبة فقط
    if (!_formKey.currentState!.validate()) return;

    // التحقق من الحقول المطلوبة يدوياً
    if (_userController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('اليوزر مطلوب')));
      return;
    }

    if (_isServerSubscriber) {
      if (_passwordController.text.trim().isEmpty) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('كلمة المرور مطلوبة')));
        return;
      }

      if (_confirmPasswordController.text.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تأكيد كلمة المرور مطلوب')),
        );
        return;
      }
    }

    if (_selectedBundleName == null || _selectedBundleName!.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('نوع الباقة مطلوب')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isServerSubscriber) {
        await _addServerSubscriber();
      } else {
        await _addLocalSubscriber();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة المشترك: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إضافة مشترك محلي (المنطق الحالي)
  Future<void> _addLocalSubscriber() async {
    // تحقق من عدم تكرار اليوزر
    final userInput = _userController.text.trim();
    final userExists = await DBHelper.instance.isUserExists(userInput);
    if (userExists) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('اليوزر موجود بالفعل، يرجى اختيار يوزر آخر'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }
    // تحقق من تطابق السعر مع الباقات
    final priceInput = _priceController.text.trim();
    String? bundleName;
    double? priceValue;
    String? bundleId;
    // إذا كان النمط "اسم - سعر"
    if (priceInput.contains('-')) {
      final parts = priceInput.split('-');
      if (parts.length == 2) {
        bundleName = parts[0].trim();
        priceValue = double.tryParse(
          parts[1].replaceAll(RegExp(r'[^0-9.]'), ''),
        );
      }
    } else {
      // محاولة مطابقة السعر فقط (مع دعم السعر المخصص)
      priceValue = double.tryParse(
        priceInput.replaceAll(RegExp(r'[^0-9.]'), ''),
      );
      final found = _subscriptions.firstWhere(
        (sub) =>
            _getEffectiveSellPrice(sub).toString() == priceValue?.toString(),
        orElse: () => {},
      );
      if (found.isNotEmpty) {
        bundleName = found['name'];
        bundleId = found['id'];
      }
    }
    if (priceValue == null || bundleName == null || bundleId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('القيمة المدخلة لا تتطابق مع أسعار الاشتراكات!'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }
    double totalDebt = double.tryParse(_debtController.text) ?? 0.0;
    final price = priceValue;
    if (!_isContinuous) {
      totalDebt += price;
    }
    // جلب نوع المدة من قاعدة البيانات
    final now = DateTime.now();
    final durationType = await DBHelper.instance.getDurationType();
    final endDate = calculateSubscriptionEndDate(now, durationType);
    int remainingDays = endDate.difference(now).inDays;
    if (remainingDays < 0) remainingDays = 0;
    final name = _nameController.text.trim();
    final username = _userController.text.trim();

    final sub = Subscriber(
      name: name.isNotEmpty
          ? name
          : username, // استخدام اليوزر إذا لم يكن هناك اسم
      totalDebt: totalDebt,
      user: username,
      subscriptionPrice: price, // سعر البيع فقط
      subscriptionType: bundleName,
      startDate: now,
      endDate: endDate,
      phone: _phoneController.text.trim(),
      notes: null,
      ip: null,
      subscriptionId: null,
      buyPrice: null,
      sourceType: 'manual', // تعيين نوع المشترك كيدوي
    );
    await widget.repository.addSubscriber(sub);
    // منطق إرسال رسالة واتساب عند إضافة مشترك جديد إذا كان خيار التنبيه مفعل ورقم الهاتف موجود
    try {
      final db = await DBHelper.instance.database;
      final notifyRow = await db.query(
        'settings',
        where: 'key = ?',
        whereArgs: ['notifyStart'],
        limit: 1,
      );
      final notifyStart =
          notifyRow.isNotEmpty &&
          (notifyRow.first['value'] == '1' ||
              notifyRow.first['value'] == true ||
              notifyRow.first['value'] == 'true');
      final phoneRaw = _phoneController.text.trim();
      String phone = phoneRaw;
      if (phone.startsWith('0')) {
        phone = '964${phone.substring(1)}';
      }
      phone = phone.replaceAll(RegExp(r'[^0-9]'), '');
      if (notifyStart && phone.isNotEmpty) {
        final startMsg = await DBHelper.instance.getMessage(
          'start_msg',
          'تم تفعيل اشتراكك {الاسم} بنجاح. نوع الاشتراك: {نوع_الاشتراك}. السعر: {سعر_الاشتراك}. تاريخ البدء: {تاريخ_البدء}. تاريخ الانتهاء: {تاريخ_الانتهاء}.',
        );
        String msg = startMsg
            .replaceAll('{الاسم}', _nameController.text.trim())
            .replaceAll('{رقم_الهاتف}', phone)
            .replaceAll('{نوع_الاشتراك}', bundleName)
            .replaceAll('{سعر_الاشتراك}', priceValue.toString())
            .replaceAll('{تاريخ_البدء}', now.toString().split(' ').first)
            .replaceAll('{تاريخ_الانتهاء}', endDate.toString().split(' ').first)
            .replaceAll('{الدين}', totalDebt.toString());
        final whatsappUrl = Uri.parse(
          'https://wa.me/$phone?text=${Uri.encodeComponent(msg)}',
        );
        print('WHATSAPP URL: $whatsappUrl');
        debugPrint('WHATSAPP URL: $whatsappUrl');
        try {
          final launched = await launchUrl(
            whatsappUrl,
            mode: LaunchMode.externalApplication,
          );
          if (!launched) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'تعذر فتح واتساب. تأكد من وجود التطبيق وصحة الرقم.',
                ),
              ),
            );
          }
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تعذر إرسال رسالة واتساب: $e')),
          );
        }
      }
    } catch (e) {
      // تجاهل الخطأ أو أظهر رسالة إذا رغبت
    }
    Navigator.of(context).pop(true);
  }

  /// إضافة مشترك في السيرفر
  Future<void> _addServerSubscriber() async {
    // التحقق من وجود سيرفرات
    final boards = await DBHelper.instance.getAllBoards();
    if (boards.isEmpty) {
      throw Exception('لا توجد سيرفرات مضافة. يرجى إضافة سيرفر أولاً.');
    }

    // اختيار أول سيرفر متاح (يمكن تحسين هذا لاحقاً)
    final board = boards.first;

    // التحقق من صحة البيانات
    final username = _userController.text.trim();
    final password = _passwordController.text.trim();
    final confirmPassword = _confirmPasswordController.text.trim();
    final name = _nameController.text.trim();

    // التحقق من تطابق كلمات المرور
    if (password != confirmPassword) {
      throw Exception('كلمات المرور غير متطابقة');
    }

    if (password.isEmpty) {
      throw Exception('يرجى إدخال كلمة مرور');
    }

    // جلب معرف الباقة
    final selectedSubscription = _subscriptions.firstWhere(
      (sub) => sub['name'] == _selectedBundleName,
      orElse: () => throw Exception('لم يتم العثور على الباقة المحددة'),
    );

    // تحضير بيانات المشترك للسيرفر
    debugPrint('[ADD USER] اليوزر: $username');
    debugPrint('[ADD USER] كلمة المرور: $password');

    final userData = {
      'username': username,
      'password': password,
      'enabled': 1,
      'profile_id': selectedSubscription['id'], // معرف الباقة
      'parent_id': board['parent_id'] ?? 204, // معرف المدير
      'site_id': null,
      'mac_auth': 0,
      'simultaneous_use': 1,
      'firstname': name.isNotEmpty
          ? name
          : username, // استخدام اليوزر إذا لم يكن هناك اسم
      'lastname': '',
      'email': '',
      'phone': _phoneController.text.trim(),
      'address': '',
      'city': '',
      'zip': '',
      'country': '',
      'state': '',
      'comment': '',
    };

    debugPrint('=== إضافة مشترك جديد ===');
    debugPrint('[NEW USER] اليوزر: $username');
    debugPrint('[NEW USER] كلمة المرور: $password');
    debugPrint('[NEW USER] البيانات الكاملة: $userData');
    debugPrint('========================');

    try {
      // تسجيل الدخول للحصول على التوكن
      final loginResponse = await api_helper.tryConnect(board);
      if (loginResponse['success'] != true) {
        throw Exception('فشل تسجيل الدخول: ${loginResponse['message']}');
      }

      final token = loginResponse['token'];

      // إرسال طلب إضافة المشترك
      final dio = Dio();

      // تجاهل خطأ شهادة SSL (مثل تسجيل الدخول)
      (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        final client = HttpClient();
        client.badCertificateCallback = (cert, host, port) {
          debugPrint('[ADD USER] تجاهل خطأ شهادة SSL للخادم: $host:$port');
          return true;
        };
        return client;
      };

      debugPrint(
        '[ADD USER] إرسال طلب إضافة المشترك إلى: https://${board['url']}/admin/api/index.php/api/user',
      );

      // تشفير البيانات مثل باقي الطلبات
      final jsonString = jsonEncode(userData);
      final payload = encryptWithOpenSSL(
        jsonString,
        'abcdefghijuklmno0123456789012345',
      );

      debugPrint('[ADD USER] البيانات قبل التشفير: $jsonString');
      debugPrint('[ADD USER] البيانات المشفرة: $payload');

      final response = await dio.post(
        'https://${board['url']}/admin/api/index.php/api/user',
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Authorization': 'Bearer $token',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Origin': 'https://${board['url']}',
            'Referer': 'https://${board['url']}/',
            'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
          },
        ),
        data: {'payload': payload},
      );

      debugPrint('[ADD USER] كود الاستجابة: ${response.statusCode}');
      debugPrint('[ADD USER] محتوى الاستجابة: ${response.data}');

      if (response.statusCode == 200 && response.data['status'] == 200) {
        // نجح إنشاء المشترك في السيرفر
        debugPrint('[ADD USER] تم إنشاء المشترك في السيرفر بنجاح');

        // بدء مزامنة صامتة لجلب البيانات الحقيقية
        await _performSilentSync(board);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة المشترك بنجاح! جاري تحديث البيانات...'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        throw Exception(
          'فشل إضافة المشترك: ${response.data['message'] ?? 'خطأ غير معروف'}',
        );
      }
    } catch (e) {
      debugPrint('[ADD USER ERROR] خطأ في إضافة المشترك: $e');
      if (e is DioException) {
        debugPrint('[ADD USER ERROR] نوع الخطأ: DioException');
        debugPrint('[ADD USER ERROR] كود الخطأ: ${e.response?.statusCode}');
        debugPrint('[ADD USER ERROR] رسالة الخطأ: ${e.response?.data}');
        debugPrint('[ADD USER ERROR] تفاصيل الخطأ: ${e.message}');
      }
      throw Exception('خطأ في الاتصال بالسيرفر: $e');
    }
  }

  /// مزامنة صامتة لجلب البيانات الحقيقية من السيرفر
  Future<void> _performSilentSync(Map<String, dynamic> board) async {
    try {
      debugPrint('[SILENT SYNC] بدء المزامنة الصامتة...');

      // استيراد دالة المزامنة من api_helper
      await syncAllFromServer(
        board: board,
        silent: true,
        progressCallback: (step, {status, count, total}) {
          debugPrint('[SILENT SYNC] $step: $status');
        },
      );

      debugPrint('[SILENT SYNC] تمت المزامنة الصامتة بنجاح');
    } catch (e) {
      debugPrint('[SILENT SYNC] خطأ في المزامنة الصامتة: $e');
      // لا نعرض خطأ للمستخدم لأنها مزامنة صامتة
      // المشترك تم إضافته في السيرفر بنجاح
    }
  }

  @override
  void initState() {
    super.initState();
    _priceFocusNode.addListener(() {
      if (_priceFocusNode.hasFocus && !_priceInitialized) {
        if (_priceController.text.isEmpty) {
          _priceController.text = '000';
          // ضع المؤشر قبل الأصفار
          _priceController.selection = TextSelection.collapsed(offset: 0);
        }
        _priceInitialized = true;
      }
    });
    _loadSubscriptions();
  }

  @override
  void dispose() {
    _priceFocusNode.dispose();
    _nameController.dispose();
    _userController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _debtController.dispose();
    _phoneController.dispose();
    _priceController.dispose();

    super.dispose();
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          // الأيقونة الرئيسية في الوسط
          Container(
            margin: const EdgeInsets.only(bottom: 18),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withValues(alpha: 0.18),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundColor: Colors.white.withValues(
                alpha: isDark ? 0.08 : 0.18,
              ),
              child: Icon(
                Icons.person_add,
                color: colorScheme.primary,
                size: 54,
              ),
            ),
          ),

          // العنوان والوصف
          Text(
            'إضافة مشترك',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
              letterSpacing: 1,
              shadows: [
                Shadow(
                  color: colorScheme.shadow.withValues(alpha: 0.13),
                  blurRadius: 4,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'إضافة مشترك جديد للنظام',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onPrimary.withValues(alpha: 0.8),
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final iconColor = Theme.of(context).iconTheme.color;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية موحدة
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                // رأس الشاشة
                _buildHeader(colorScheme, isDark),

                // محتوى الشاشة
                Expanded(
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      children: [
                        // اختيار نوع المشترك
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                (_isServerSubscriber
                                        ? Colors.green
                                        : Colors.blue)
                                    .withValues(alpha: 0.15),
                                (_isServerSubscriber
                                        ? Colors.teal
                                        : Colors.indigo)
                                    .withValues(alpha: 0.1),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color:
                                  (_isServerSubscriber
                                          ? Colors.green
                                          : Colors.blue)
                                      .withValues(alpha: 0.4),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    (_isServerSubscriber
                                            ? Colors.green
                                            : Colors.blue)
                                        .withValues(alpha: 0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              // أزرار الاختيار
                              Row(
                                children: [
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _isServerSubscriber = false;
                                        });
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 12,
                                          horizontal: 16,
                                        ),
                                        decoration: BoxDecoration(
                                          color: !_isServerSubscriber
                                              ? Colors.blue.withValues(
                                                  alpha: 0.2,
                                                )
                                              : Colors.transparent,
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          border: Border.all(
                                            color: !_isServerSubscriber
                                                ? Colors.blue
                                                : Colors.grey.withValues(
                                                    alpha: 0.3,
                                                  ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.person_add,
                                              color: !_isServerSubscriber
                                                  ? Colors.blue.shade700
                                                  : Colors.grey,
                                              size: 20,
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              'محلي',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: !_isServerSubscriber
                                                    ? Colors.blue.shade800
                                                    : Colors.grey,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _isServerSubscriber = true;
                                        });
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 12,
                                          horizontal: 16,
                                        ),
                                        decoration: BoxDecoration(
                                          color: _isServerSubscriber
                                              ? Colors.green.withValues(
                                                  alpha: 0.2,
                                                )
                                              : Colors.transparent,
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          border: Border.all(
                                            color: _isServerSubscriber
                                                ? Colors.green
                                                : Colors.grey.withValues(
                                                    alpha: 0.3,
                                                  ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.cloud_upload,
                                              color: _isServerSubscriber
                                                  ? Colors.green.shade700
                                                  : Colors.grey,
                                              size: 20,
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              'سيرفر',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: _isServerSubscriber
                                                    ? Colors.green.shade800
                                                    : Colors.grey,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              // الوصف
                              Text(
                                _isServerSubscriber
                                    ? 'سيتم إنشاء هذا المشترك في السيرفر ومزامنته محلياً'
                                    : 'سيتم إنشاء هذا المشترك محلياً ويمكن إدارته بدون الحاجة للسيرفر',
                                style: TextStyle(
                                  color:
                                      (_isServerSubscriber
                                              ? Colors.green
                                              : Colors.blue)
                                          .shade600,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _nameController,
                                style: TextStyle(
                                  color: Theme.of(
                                    context,
                                  ).textTheme.bodyLarge?.color,
                                ),
                                decoration: InputDecoration(
                                  labelText: 'الاسم (اختياري)',
                                  labelStyle: TextStyle(
                                    color: Theme.of(context).hintColor,
                                  ),
                                  prefixIcon: Icon(
                                    Icons.person,
                                    color: iconColor,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  filled: true,
                                  fillColor: Theme.of(context).cardColor,
                                ),
                                validator: null, // الاسم اختياري
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 14),
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _userController,
                                style: TextStyle(
                                  color: Theme.of(
                                    context,
                                  ).textTheme.bodyLarge?.color,
                                ),
                                decoration: InputDecoration(
                                  labelText: 'اليوزر',
                                  labelStyle: TextStyle(
                                    color: Theme.of(context).hintColor,
                                  ),
                                  prefixIcon: Icon(
                                    Icons.account_circle,
                                    color: iconColor,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  filled: true,
                                  fillColor: Theme.of(context).cardColor,
                                ),
                              ),
                            ),
                          ],
                        ),

                        // حقول الباسورد (للسيرفر فقط)
                        if (_isServerSubscriber) ...[
                          const SizedBox(height: 14),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _passwordController,
                                  obscureText: true,
                                  style: TextStyle(
                                    color: Theme.of(
                                      context,
                                    ).textTheme.bodyLarge?.color,
                                  ),
                                  decoration: InputDecoration(
                                    labelText: 'كلمة المرور',
                                    labelStyle: TextStyle(
                                      color: Theme.of(context).hintColor,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.lock,
                                      color: iconColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    filled: true,
                                    fillColor: Theme.of(context).cardColor,
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'يرجى إدخال كلمة المرور';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 14),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _confirmPasswordController,
                                  obscureText: true,
                                  style: TextStyle(
                                    color: Theme.of(
                                      context,
                                    ).textTheme.bodyLarge?.color,
                                  ),
                                  decoration: InputDecoration(
                                    labelText: 'تأكيد كلمة المرور',
                                    labelStyle: TextStyle(
                                      color: Theme.of(context).hintColor,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.lock_outline,
                                      color: iconColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    filled: true,
                                    fillColor: Theme.of(context).cardColor,
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'يرجى تأكيد كلمة المرور';
                                    }
                                    if (value != _passwordController.text) {
                                      return 'كلمات المرور غير متطابقة';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],

                        const SizedBox(height: 14),
                        Row(
                          children: [
                            Expanded(
                              child: Directionality(
                                textDirection: TextDirection.rtl,
                                child: TextFormField(
                                  controller: _priceController,
                                  focusNode: _priceFocusNode,
                                  keyboardType: TextInputType.number,
                                  textAlign: TextAlign.right,
                                  style: TextStyle(
                                    color: Theme.of(
                                      context,
                                    ).textTheme.bodyLarge?.color,
                                  ),
                                  decoration: InputDecoration(
                                    labelText: 'سعر الاشتراك',
                                    labelStyle: TextStyle(
                                      color: Theme.of(context).hintColor,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.attach_money,
                                      color: iconColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    filled: true,
                                    fillColor: Theme.of(context).cardColor,
                                    suffixIcon: IconButton(
                                      icon: const Icon(Icons.arrow_drop_down),
                                      onPressed: _subscriptions.isEmpty
                                          ? null
                                          : () async {
                                              final selected = await showModalBottomSheet<Map<String, dynamic>>(
                                                context: context,
                                                shape:
                                                    const RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.vertical(
                                                            top:
                                                                Radius.circular(
                                                                  24,
                                                                ),
                                                          ),
                                                    ),
                                                builder: (ctx) => Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    const SizedBox(height: 16),
                                                    const Text(
                                                      'اختر باقة الاشتراك',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 18,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 8),
                                                    Flexible(
                                                      child: ListView.separated(
                                                        shrinkWrap: true,
                                                        itemCount:
                                                            _subscriptions
                                                                .length,
                                                        separatorBuilder:
                                                            (_, __) =>
                                                                const Divider(
                                                                  height: 0,
                                                                ),
                                                        itemBuilder: (ctx, idx) {
                                                          final sub =
                                                              _subscriptions[idx];
                                                          final effectivePrice =
                                                              _getEffectiveSellPrice(
                                                                sub,
                                                              );
                                                          final isSelected =
                                                              (_priceController
                                                                  .text ==
                                                              effectivePrice
                                                                  .toString());
                                                          return ListTile(
                                                            leading: const Icon(
                                                              Icons.star,
                                                              color:
                                                                  Colors.amber,
                                                            ),
                                                            title: Text(
                                                              sub['name'] ?? '',
                                                            ),
                                                            subtitle: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                  '${effectivePrice.toStringAsFixed(0)} د.ع',
                                                                ),
                                                                if (sub['custom_sell_price'] !=
                                                                        null &&
                                                                    sub['custom_sell_price'] >
                                                                        0)
                                                                  Container(
                                                                    margin:
                                                                        const EdgeInsets.only(
                                                                          top:
                                                                              2,
                                                                        ),
                                                                    padding: const EdgeInsets.symmetric(
                                                                      horizontal:
                                                                          4,
                                                                      vertical:
                                                                          1,
                                                                    ),
                                                                    decoration: BoxDecoration(
                                                                      color: Colors
                                                                          .blue
                                                                          .withValues(
                                                                            alpha:
                                                                                0.1,
                                                                          ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                            3,
                                                                          ),
                                                                      border: Border.all(
                                                                        color: Colors
                                                                            .blue
                                                                            .withValues(
                                                                              alpha: 0.3,
                                                                            ),
                                                                      ),
                                                                    ),
                                                                    child: Text(
                                                                      'مخصص',
                                                                      style: TextStyle(
                                                                        fontSize:
                                                                            8,
                                                                        color: Colors
                                                                            .blue,
                                                                        fontWeight:
                                                                            FontWeight.bold,
                                                                      ),
                                                                    ),
                                                                  ),
                                                              ],
                                                            ),
                                                            trailing: isSelected
                                                                ? const Icon(
                                                                    Icons
                                                                        .check_circle,
                                                                    color: Colors
                                                                        .green,
                                                                  )
                                                                : null,
                                                            onTap: () =>
                                                                Navigator.of(
                                                                  ctx,
                                                                ).pop(sub),
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                    const SizedBox(height: 16),
                                                  ],
                                                ),
                                              );
                                              if (selected != null) {
                                                final effectivePrice =
                                                    _getEffectiveSellPrice(
                                                      selected,
                                                    );
                                                _priceController.text =
                                                    effectivePrice
                                                        .toStringAsFixed(0);
                                                setState(() {
                                                  _selectedBundleName =
                                                      selected['name'];
                                                });
                                              }
                                            },
                                    ),
                                  ),
                                  validator: (v) =>
                                      v == null || v.isEmpty || v == '000'
                                      ? 'سعر الاشتراك مطلوب'
                                      : null,
                                  readOnly: false,
                                  onTap: () async {
                                    if (_subscriptions.isEmpty) return;
                                    final selected = await showModalBottomSheet<Map<String, dynamic>>(
                                      context: context,
                                      shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(24),
                                        ),
                                      ),
                                      builder: (ctx) => Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const SizedBox(height: 16),
                                          const Text(
                                            'اختر باقة الاشتراك',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Flexible(
                                            child: ListView.separated(
                                              shrinkWrap: true,
                                              itemCount: _subscriptions.length,
                                              separatorBuilder: (_, __) =>
                                                  const Divider(height: 0),
                                              itemBuilder: (ctx, idx) {
                                                final sub = _subscriptions[idx];
                                                final effectivePrice =
                                                    _getEffectiveSellPrice(sub);
                                                final isSelected =
                                                    (_priceController.text
                                                        .contains(
                                                          sub['name'] ?? '',
                                                        ) &&
                                                    _priceController.text
                                                        .contains(
                                                          effectivePrice
                                                              .toString(),
                                                        ));
                                                return ListTile(
                                                  leading: const Icon(
                                                    Icons.star,
                                                    color: Colors.amber,
                                                  ),
                                                  title: Text(
                                                    sub['name'] ?? '',
                                                  ),
                                                  subtitle: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        '${effectivePrice.toStringAsFixed(0)} د.ع',
                                                      ),
                                                      if (sub['custom_sell_price'] !=
                                                              null &&
                                                          sub['custom_sell_price'] >
                                                              0)
                                                        Container(
                                                          margin:
                                                              const EdgeInsets.only(
                                                                top: 2,
                                                              ),
                                                          padding:
                                                              const EdgeInsets.symmetric(
                                                                horizontal: 4,
                                                                vertical: 1,
                                                              ),
                                                          decoration: BoxDecoration(
                                                            color: Colors.blue
                                                                .withValues(
                                                                  alpha: 0.1,
                                                                ),
                                                            borderRadius:
                                                                BorderRadius.circular(
                                                                  3,
                                                                ),
                                                            border: Border.all(
                                                              color: Colors.blue
                                                                  .withValues(
                                                                    alpha: 0.3,
                                                                  ),
                                                            ),
                                                          ),
                                                          child: Text(
                                                            'مخصص',
                                                            style: TextStyle(
                                                              fontSize: 8,
                                                              color:
                                                                  Colors.blue,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                  trailing: isSelected
                                                      ? const Icon(
                                                          Icons.check_circle,
                                                          color: Colors.green,
                                                        )
                                                      : null,
                                                  onTap: () => Navigator.of(
                                                    ctx,
                                                  ).pop(sub),
                                                );
                                              },
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                        ],
                                      ),
                                    );
                                    if (selected != null) {
                                      final effectivePrice =
                                          _getEffectiveSellPrice(selected);
                                      _priceController.text = effectivePrice
                                          .toStringAsFixed(0);
                                      setState(() {
                                        _selectedBundleName = selected['name'];
                                      });
                                    }
                                  },
                                ),
                              ),
                            ),
                            const SizedBox(width: 10),
                            const Text('واصل'),
                            Checkbox(
                              value: _isContinuous,
                              onChanged: (v) =>
                                  setState(() => _isContinuous = v ?? false),
                            ),
                          ],
                        ),
                        const SizedBox(height: 14),
                        TextFormField(
                          controller: _debtController,
                          keyboardType: TextInputType.number,
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                          decoration: InputDecoration(
                            labelText: 'الديون السابقة',
                            labelStyle: TextStyle(
                              color: Theme.of(context).hintColor,
                            ),
                            prefixIcon: Icon(Icons.qr_code, color: iconColor),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: Theme.of(context).cardColor,
                          ),
                        ),
                        const SizedBox(height: 14),
                        TextFormField(
                          controller: _phoneController,
                          keyboardType: TextInputType.phone,
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                          decoration: InputDecoration(
                            labelText: 'رقم الهاتف (اختياري)',
                            labelStyle: TextStyle(
                              color: Theme.of(context).hintColor,
                            ),
                            prefixIcon: Icon(Icons.phone, color: iconColor),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: Theme.of(context).cardColor,
                          ),
                          validator: (v) {
                            final phone =
                                v?.replaceAll(RegExp(r'[^0-9]'), '') ?? '';
                            if (phone.isNotEmpty && phone.length != 11) {
                              return 'رقم الهاتف يجب أن يكون 11 رقم بدون رموز أو فواصل';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 24),
                        SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _save,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(
                                context,
                              ).colorScheme.primary,
                              foregroundColor: Theme.of(
                                context,
                              ).colorScheme.onPrimary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                            ),
                            child: _isLoading
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                Theme.of(
                                                  context,
                                                ).colorScheme.onPrimary,
                                              ),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        _isServerSubscriber
                                            ? 'إضافة في السيرفر...'
                                            : 'إضافة محلياً...',
                                        style: const TextStyle(fontSize: 18),
                                      ),
                                    ],
                                  )
                                : Text(
                                    _isServerSubscriber
                                        ? 'إضافة في السيرفر'
                                        : 'إضافة محلياً',
                                    style: const TextStyle(fontSize: 18),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
