import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'simple_root_screen.dart';
import 'features/subscribers/domain/subscribers_repository.dart';
import 'features/subscribers/domain/subscribers_repository_impl.dart';
import 'features/subscribers/data/subscribers_storage_impl.dart';
import 'features/main_home_screen.dart';
import 'db_helper.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import 'utils/api_helper.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'services/account_service.dart';
import 'services/deep_link_service.dart';

// تحديث حالة الاتصال العامة (أي سيرفر أو لوحة متصل)
Future<void> _updateAnyDeviceConnected() async {
  final servers = await DBHelper.instance.getAllServers();
  final boards = await DBHelper.instance.getAllBoards();
  final anyConnected =
      servers.any(
        (s) =>
            s['connected'] == 1 ||
            s['connected'] == true ||
            s['connected'] == '1',
      ) ||
      boards.any(
        (b) =>
            b['connected'] == 1 ||
            b['connected'] == true ||
            b['connected'] == '1',
      );
  anyDeviceConnectedNotifier.value = anyConnected;
}

// تعريف RouteObserver ليكون متاحًا للتطبيق كله
final RouteObserver<ModalRoute<void>> routeObserver =
    RouteObserver<ModalRoute<void>>();

// تعريف navigatorKey عالمي للوصول للـ context في أي مكان
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// تعريف ValueNotifier عالمي لحالة الاتصال بأي سيرفر أو لوحة
final ValueNotifier<bool> anyDeviceConnectedNotifier = ValueNotifier(false);
final ValueNotifier<bool> syncCompletedNotifier = ValueNotifier(false);

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  debugPrint('1. WidgetsFlutterBinding initialized');
  try {
    debugPrint('2. Initializing Supabase...');
    // إعداد Supabase
    await Supabase.initialize(
      url: 'https://iwtvsvfqmafsziqnoekm.supabase.co',
      anonKey:
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3dHZzdmZxbWFmc3ppcW5vZWttIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMjM2NzUsImV4cCI6MjA2ODc5OTY3NX0.9ho9O3Bk-iziVQBSiQ5X5V9E45KqEzJ2tuMoSu5kNKg',
    );

    debugPrint('✅ تم إعداد Supabase بنجاح');
    await _updateAnyDeviceConnected();
    runApp(const MyApp());
  } catch (e, stack) {
    debugPrint('Error initializing Supabase: $e');
    debugPrint('Stack trace: $stack');
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text('Supabase initialization failed: ${e.toString()}'),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool _autoSyncStarted = false;

  late final SubscribersRepository subscribersRepository;
  ThemeMode themeMode = ThemeMode.system;
  Timer? _autoSyncTimer;
  bool _isSyncing = false;

  // متغيرات فحص الحساب الدوري
  Timer? _accountCheckTimer;
  bool _accountCheckStarted = false;

  void _toggleTheme() async {
    debugPrint(
      'تم استدعاء _toggleTheme. القيمة الحالية: '
      '${themeMode == ThemeMode.dark ? 'dark' : 'light'}',
    );
    setState(() {
      if (themeMode == ThemeMode.dark) {
        themeMode = ThemeMode.light;
      } else {
        themeMode = ThemeMode.dark;
      }
    });
    debugPrint(
      'تم تغيير themeMode إلى: '
      '${themeMode == ThemeMode.dark ? 'dark' : 'light'}',
    );
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('themeMode', themeMode.toString().split('.').last);
  }

  void _showThemeStatus(BuildContext context) {
    String text;
    if (themeMode == ThemeMode.dark) {
      text = 'تم تفعيل الوضع الليلي';
    } else {
      text = 'تم تفعيل الوضع النهاري';
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(text, textAlign: TextAlign.center),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    subscribersRepository = SubscribersRepositoryImpl(SubscribersStorageImpl());
    _initThemeMode();
    // تم نقل منطق التحقق من تسجيل الدخول إلى SimpleRootScreen
  }

  void _startAutoSync() {
    if (_autoSyncStarted) {
      debugPrint('[SYNC-DEBUG] _startAutoSync: already started, skip');
      return;
    }
    _autoSyncStarted = true;
    debugPrint('[SYNC-DEBUG] _startAutoSync called');
    _autoSyncTimer?.cancel();
    debugPrint('[SYNC-DEBUG] _autoSyncTimer canceled (if existed)');
    _autoSyncTimer = Timer.periodic(const Duration(minutes: 10), (_) async {
      debugPrint(
        '[SYNC-DEBUG] Timer.periodic fired, calling _autoSyncAllBoards',
      );
      await _autoSyncAllBoards();
    });
    debugPrint('[SYNC-DEBUG] _autoSyncTimer started (3 min interval)');
    // تنفيذ أول مزامنة مباشرة عند بدء التطبيق
    _autoSyncAllBoards();
    debugPrint('[SYNC-DEBUG] First _autoSyncAllBoards called immediately');

    debugPrint('[SYNC] المزامنة التلقائية نشطة مع Supabase');

    // إضافة منطق إعادة المحاولة التلقائية للاتصال باللوحات المتصلة
    _retryConnectBoards();

    // بدء فحص الحساب الدوري
    _startAccountCheck();
  }

  void _startAccountCheck() {
    if (_accountCheckStarted) {
      debugPrint('[ACCOUNT_CHECK] _startAccountCheck: already started, skip');
      return;
    }
    _accountCheckStarted = true;
    debugPrint('[ACCOUNT_CHECK] بدء فحص الحساب الدوري...');

    _accountCheckTimer?.cancel();
    _accountCheckTimer = Timer.periodic(const Duration(minutes: 15), (_) async {
      debugPrint(
        '[ACCOUNT_CHECK] Timer.periodic fired, calling _checkAccountStatus',
      );
      await _checkAccountStatus();
    });

    debugPrint('[ACCOUNT_CHECK] تم بدء فحص الحساب الدوري (كل 15 دقيقة)');

    // تنفيذ أول فحص مباشرة عند بدء التطبيق
    _checkAccountStatus();
    debugPrint('[ACCOUNT_CHECK] تم تنفيذ أول فحص للحساب');
  }

  Future<void> _checkAccountStatus() async {
    try {
      debugPrint('[ACCOUNT_CHECK] بدء فحص حالة الحساب...');

      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null) {
        debugPrint('[ACCOUNT_CHECK] لا يوجد مستخدم مسجل دخول - تخطي الفحص');
        return;
      }

      debugPrint('[ACCOUNT_CHECK] فحص وجود الحساب للمستخدم: ${currentUser.id}');
      final accountExists = await AccountService.checkAccountExists(
        currentUser.id,
      );

      if (!accountExists) {
        debugPrint(
          '❌ [ACCOUNT_CHECK] الحساب غير موجود في Supabase - إنهاء الجلسة',
        );

        // إنهاء الجلسة وتنظيف البيانات
        await AccountService.signOutAndCleanup();

        // إيقاف المؤقتات
        _accountCheckTimer?.cancel();
        _autoSyncTimer?.cancel();
        _accountCheckStarted = false;
        _autoSyncStarted = false;

        // التوجه لشاشة تسجيل الدخول
        if (mounted && navigatorKey.currentContext != null) {
          debugPrint('[ACCOUNT_CHECK] التوجه لشاشة تسجيل الدخول...');
          Navigator.of(
            navigatorKey.currentContext!,
          ).pushNamedAndRemoveUntil('/login', (route) => false);
        }
      } else {
        debugPrint('✅ [ACCOUNT_CHECK] الحساب موجود وصالح');
      }
    } catch (e) {
      debugPrint('❌ [ACCOUNT_CHECK] خطأ في فحص حالة الحساب: $e');
    }
  }

  void _retryConnectBoards() async {
    final boards = await DBHelper.instance.getAllBoards();
    final connectedBoards = boards
        .where(
          (b) =>
              b['connected'] == 1 ||
              b['connected'] == true ||
              b['connected'] == '1',
        )
        .toList();
    if (connectedBoards.isEmpty) return;
    // المحاولة الأولى بعد دقيقة
    Future.delayed(const Duration(minutes: 1), () async {
      await _retryConnectOnce(connectedBoards);
      // المحاولة الثانية بعد دقيقتين من الأولى
      Future.delayed(const Duration(minutes: 2), () async {
        await _retryConnectOnce(connectedBoards);
        // المحاولة الثالثة بعد ثلاث دقائق من الثانية
        Future.delayed(const Duration(minutes: 3), () async {
          await _retryConnectOnce(connectedBoards);
        });
      });
    });
  }

  Future<void> _retryConnectOnce(List boards) async {
    for (final board in boards) {
      try {
        if (board['connected'] == 1 ||
            board['connected'] == true ||
            board['connected'] == '1') {
          // استدعاء tryConnect من boards_list_screen.dart
          await tryConnect(board);
        }
      } catch (e) {
        debugPrint(
          '[RETRY CONNECT][ERROR] فشل إعادة الاتصال باللوحة: ${board['name']} - $e',
        );
      }
    }
  }

  Future<void> _autoSyncAllBoards() async {
    debugPrint('[SYNC-DEBUG] _autoSyncAllBoards called');
    if (_isSyncing) {
      debugPrint('[SYNC-DEBUG] _autoSyncAllBoards: already syncing, return');
      return;
    }
    _isSyncing = true;
    try {
      final boards = await DBHelper.instance.getAllBoards();
      for (final board in boards) {
        if (board['connected'] == 1 ||
            board['connected'] == true ||
            board['connected'] == '1') {
          try {
            debugPrint('[SYNC-DEBUG] Syncing board: ${board['name']}');
            // استدعاء دالة المزامنة المركزية لكل لوحة متصلة
            final changes = await syncAllFromServer(
              board: board,
              silent: false,
            );
            debugPrint(
              '[AUTO SYNC] تمت مزامنة اللوحة: \x1B[32m${board['name']}\x1B[0m',
            );
            final ctx = navigatorKey.currentContext;
            if (changes.isNotEmpty && ctx != null && mounted) {
              if (changes.length <= 3) {
                for (final msg in changes) {
                  // إشعار منفصل لكل تغيير
                  ScaffoldMessenger.of(ctx).showSnackBar(
                    SnackBar(
                      content: Text(msg, textAlign: TextAlign.center),
                      backgroundColor: msg.startsWith('تمت إضافة')
                          ? Colors.green
                          : null,
                      duration: const Duration(seconds: 3),
                      behavior: SnackBarBehavior.floating,
                      margin: const EdgeInsets.symmetric(
                        horizontal: 40,
                        vertical: 20,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                  );
                  await Future.delayed(const Duration(milliseconds: 500));
                }
              } else {
                // إشعار ملخص واحد
                final summary =
                    'تمت ${changes.length} تغييرات أثناء المزامنة:\n' +
                    changes.take(3).join('\n') +
                    (changes.length > 3 ? '\nوالمزيد...' : '');
                ScaffoldMessenger.of(ctx).showSnackBar(
                  SnackBar(
                    content: Text(summary, textAlign: TextAlign.center),
                    duration: const Duration(seconds: 5),
                    behavior: SnackBarBehavior.floating,
                    margin: const EdgeInsets.symmetric(
                      horizontal: 40,
                      vertical: 20,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                );
              }
            }
          } catch (e) {
            debugPrint(
              '[AUTO SYNC][ERROR] فشل مزامنة اللوحة: ${board['name']} - $e',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('[AUTO SYNC][ERROR] $e');
    } finally {
      _isSyncing = false;
      debugPrint('[SYNC-DEBUG] _autoSyncAllBoards finished, _isSyncing=false');
      // بعد نجاح المزامنة التلقائية، حدث جميع الشاشات المرتبطة
      syncCompletedNotifier.value = true;
      debugPrint(
        '[SYNC-DEBUG] تم اكتمال المزامنة بنجاح عند: ${DateTime.now()}',
      );
    }
  }

  Future<void> _initThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final mode = prefs.getString('themeMode');
    if (mode == null) {
      // أول تشغيل: اعتمد على النظام
      final brightness =
          WidgetsBinding.instance.platformDispatcher.platformBrightness;
      setState(() {
        themeMode = brightness == Brightness.dark
            ? ThemeMode.dark
            : ThemeMode.light;
      });
    } else {
      setState(() {
        if (mode == 'dark') {
          themeMode = ThemeMode.dark;
        } else {
          themeMode = ThemeMode.light;
        }
      });
    }
  }

  @override
  void dispose() {
    debugPrint('[SYNC-DEBUG] _MyAppState.dispose called, canceling timers');
    _autoSyncTimer?.cancel();
    _accountCheckTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // وضع NotificationListener حول MaterialApp لضمان استقبال الإشعارات دائمًا
    return NotificationListener<ServerStatusChangedNotification>(
      onNotification: (notification) {
        _updateAnyDeviceConnected();
        return true;
      },
      child: NotificationListener<BoardStatusChangedNotification>(
        onNotification: (notification) {
          _updateAnyDeviceConnected();
          return true;
        },
        child: MaterialApp(
          navigatorKey: navigatorKey,
          title: 'iTower',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          ),
          darkTheme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.deepPurple,
              brightness: Brightness.dark,
            ),
            brightness: Brightness.dark,
          ),
          themeMode: themeMode,
          locale: const Locale('ar', ''),
          supportedLocales: const [Locale('ar', '')],
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          navigatorObservers: [routeObserver],
          home: Directionality(
            textDirection: TextDirection.rtl,
            child: SimpleRootScreen(
              themeMode: themeMode,
              onToggleTheme: _toggleTheme,
              showThemeStatus: _showThemeStatus,
              onLoginSuccess: () {
                _startAutoSync();
                _startAccountCheck();
              },
              onLogout: () {
                _autoSyncTimer?.cancel();
                _accountCheckTimer?.cancel();
                _autoSyncStarted = false;
                _accountCheckStarted = false;
              },
            ),
          ),
        ),
      ),
    );
  }

  // تحديث حالة الاتصال العامة (أي سيرفر أو لوحة متصل)
  Future<void> _updateAnyDeviceConnected() async {
    final servers = await DBHelper.instance.getAllServers();
    final boards = await DBHelper.instance.getAllBoards();
    final anyConnected =
        servers.any(
          (s) =>
              s['connected'] == 1 ||
              s['connected'] == true ||
              s['connected'] == '1',
        ) ||
        boards.any(
          (b) =>
              b['connected'] == 1 ||
              b['connected'] == true ||
              b['connected'] == '1',
        );
    anyDeviceConnectedNotifier.value = anyConnected;
  }

  // منطق مركزي لتغيير التدرج اللوني بناءً على الرسائل في جميع الشاشات
  void updateGradientByMessage(String? message) {
    if (message == 'تم الاتصال بنجاح (الاتصال سيبقى مفتوحًا حتى تضغط قطع)') {
      anyDeviceConnectedNotifier.value = true;
    } else if (message == 'تم قطع الاتصال مع السيرفر') {
      anyDeviceConnectedNotifier.value = false;
    }
  }
}
