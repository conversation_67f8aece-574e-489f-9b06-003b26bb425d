# سكريبت Python لتوليد أكواد تفعيل عشوائية معقدة وإضافتها إلى Firestore
# تحتاج إلى تثبيت firebase-admin: pip install firebase-admin

import random
import string
import firebase_admin
from firebase_admin import credentials, firestore

# إعداد الاتصال بـ Firebase (ضع مسار ملف الخدمة الخاص بك)
cred = credentials.Certificate('path/to/serviceAccountKey.json')
firebase_admin.initialize_app(cred)
db = firestore.client()

# إعدادات الباقات
packages = [
    {'type': 'month', 'days': 30, 'amount': 5000},
    {'type': '3months', 'days': 90, 'amount': 14000},
    {'type': '6months', 'days': 180, 'amount': 27000},
    {'type': 'year', 'days': 365, 'amount': 50000},
]

# دالة توليد كود معقد

def generate_code(length=10):
    chars = string.ascii_uppercase + string.digits + string.ascii_lowercase
    return ''.join(random.choices(chars, k=length))

# توليد وإضافة الأكواد
for pkg in packages:
    for _ in range(10):
        code = generate_code(12)
        doc_ref = db.collection('activation_codes').document(code)
        doc_ref.set({
            'days': pkg['days'],
            'type': pkg['type'],
            'amount': pkg['amount'],
            'used': False,
            'usedBy': '',
            'usedAt': None
        })
        print(f"تم إنشاء الكود: {code} لباقه {pkg['type']}")

print('تم إنشاء جميع الأكواد بنجاح!')
