#!/bin/bash

# سكريبت تنظيف ملفات Firebase غير المطلوبة
# تشغيل هذا السكريبت بعد التأكد من عمل التطبيق بـ Supabase

echo "🧹 بدء تنظيف ملفات Firebase..."

# 1. حذف ملفات إعداد Firebase
echo "📁 حذف ملفات إعداد Firebase..."
if [ -f "android/app/google-services.json" ]; then
    rm "android/app/google-services.json"
    echo "✅ تم حذف google-services.json"
else
    echo "ℹ️  google-services.json غير موجود"
fi

if [ -f "ios/Runner/GoogleService-Info.plist" ]; then
    rm "ios/Runner/GoogleService-Info.plist"
    echo "✅ تم حذف GoogleService-Info.plist"
else
    echo "ℹ️  GoogleService-Info.plist غير موجود"
fi

# 2. حذف ملفات وثائق Firebase
echo "📄 حذف ملفات وثائق Firebase..."
files_to_remove=(
    "firebase_security_rules.md"
    "firebase_storage_rules_simple.txt"
    "FIREBASE_STORAGE_SETUP.md"
    "firebase_rules.txt"
)

for file in "${files_to_remove[@]}"; do
    if [ -f "$file" ]; then
        rm "$file"
        echo "✅ تم حذف $file"
    else
        echo "ℹ️  $file غير موجود"
    fi
done

# 3. حذف مجلدات Firebase إذا كانت موجودة
echo "📂 حذف مجلدات Firebase..."
if [ -d "firebase" ]; then
    rm -rf "firebase"
    echo "✅ تم حذف مجلد firebase"
else
    echo "ℹ️  مجلد firebase غير موجود"
fi

# 4. تنظيف cache Flutter
echo "🔄 تنظيف cache Flutter..."
flutter clean
echo "✅ تم تنظيف cache Flutter"

# 5. إعادة تحميل dependencies
echo "📦 إعادة تحميل dependencies..."
flutter pub get
echo "✅ تم تحميل dependencies"

echo ""
echo "🎉 تم الانتهاء من تنظيف ملفات Firebase!"
echo ""
echo "📋 ملخص التغييرات:"
echo "   ✅ تم إزالة جميع ملفات إعداد Firebase"
echo "   ✅ تم إزالة جميع ملفات وثائق Firebase"
echo "   ✅ تم تنظيف cache Flutter"
echo "   ✅ تم إعادة تحميل dependencies"
echo ""
echo "🚀 التطبيق الآن يعتمد كلياً على Supabase!"
echo ""
echo "⚠️  تذكير: تأكد من تشغيل التطبيق واختباره قبل حذف هذا السكريبت"
