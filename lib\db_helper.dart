import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'features/subscribers/data/subscriber_model.dart';
import 'features/subscribers/data/transaction_model.dart' as app_transaction;

class DBHelper {
  // ŘĽŮŘ´Ř§ŘĄ ŘŹŘŻŮŮ Ř§ŮŘŁŘŹŮŘ˛ŘŠ
  Future<void> createDevicesTableIfNotExists() async {
    final db = await instance.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ip TEXT NOT NULL,
        user TEXT NOT NULL,
        pass TEXT NOT NULL,
        name TEXT,
        model TEXT,
        version TEXT,
        status TEXT,
        lastSeen TEXT,
        details TEXT
      )
    ''');

    // التأكد من وجود جميع الأعمدة المطلوبة
    await _ensureAllDevicesColumns(db);
  }

  Future<int> insertDevice(Map<String, dynamic> device) async {
    final db = await instance.database;
    return await db.insert('devices', device);
  }

  Future<List<Map<String, dynamic>>> getAllDevices() async {
    final db = await instance.database;
    return await db.query('devices', orderBy: 'id DESC');
  }

  Future<int> deleteDevice(int id) async {
    final db = await instance.database;
    return await db.delete('devices', where: 'id = ?', whereArgs: [id]);
  }

  Future<int> updateDevice(int id, Map<String, dynamic> device) async {
    final db = await instance.database;
    return await db.update('devices', device, where: 'id = ?', whereArgs: [id]);
  }

  Future<Map<String, dynamic>?> getDeviceByIp(String ip) async {
    final db = await instance.database;
    final result = await db.query(
      'devices',
      where: 'ip = ?',
      whereArgs: [ip],
      limit: 1,
    );
    return result.isNotEmpty ? result.first : null;
  }

  // دالة لضمان وجود جميع الأعمدة المطلوبة في جدول الأجهزة
  Future<void> _ensureAllDevicesColumns(Database db) async {
    try {
      final columns = await db.rawQuery("PRAGMA table_info(devices)");
      final columnNames = columns.map((c) => c['name'] as String).toList();

      // قائمة الأعمدة المطلوبة
      final requiredColumns = {
        'name': 'TEXT',
        'custom_name': 'TEXT', // اسم مخصص من المستخدم
        'details': 'TEXT',
        'lastSeen': 'TEXT',
        'status': 'TEXT',
        'model': 'TEXT',
        'version': 'TEXT',
      };

      // إضافة الأعمدة المفقودة
      for (final entry in requiredColumns.entries) {
        if (!columnNames.contains(entry.key)) {
          print('[DB] إضافة عمود ${entry.key} إلى جدول الأجهزة');
          await db.execute(
            'ALTER TABLE devices ADD COLUMN ${entry.key} ${entry.value};',
          );
        }
      }
    } catch (e) {
      print('[DB] خطأ أثناء التأكد من أعمدة جدول الأجهزة: $e');
    }
  }

  // دالة لضمان وجود جميع الأعمدة المطلوبة في جدول الباقات
  Future<void> _ensureAllSubscriptionsColumns(Database db) async {
    try {
      final columns = await db.rawQuery("PRAGMA table_info(subscriptions)");
      final columnNames = columns.map((c) => c['name'] as String).toList();

      // قائمة الأعمدة المطلوبة
      final requiredColumns = {
        'custom_buy_price': 'REAL', // سعر شراء مخصص من المستخدم (للتوافق)
        'custom_sell_price': 'REAL', // سعر بيع مخصص من المستخدم
      };

      // إضافة الأعمدة المفقودة
      for (final entry in requiredColumns.entries) {
        if (!columnNames.contains(entry.key)) {
          print('[DB] إضافة عمود ${entry.key} إلى جدول الباقات');
          await db.execute(
            'ALTER TABLE subscriptions ADD COLUMN ${entry.key} ${entry.value};',
          );
        }
      }
    } catch (e) {
      print('[DB] خطأ أثناء التأكد من أعمدة جدول الباقات: $e');
    }
  }

  Future<List<app_transaction.Transaction>> getAllTransactions() async {
    final db = await instance.database;
    final result = await db.query('transactions', orderBy: 'date DESC');
    return result
        .map((map) => app_transaction.Transaction.fromMap(map))
        .toList();
  }

  static final DBHelper instance = DBHelper._init();
  static Database? _database;

  DBHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('itower.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);
    final db = await openDatabase(
      path,
      version: 3,
      onCreate: _createDB,
      onUpgrade: _upgradeDB,
    );
    // ŘŞŘŁŮŘŻ ŮŮ ŮŘŹŮŘŻ ŘšŮŮŘŻ active ŮŮ ŘŹŘŻŮŮ boards ŘšŮŘŻ ŮŮ ŮŘŞŘ­
    await _ensureActiveColumnInBoards(db);
    // ŘŞŘŁŮŘŻ ŮŮ ŮŘŹŮŘŻ ŘšŮŮŘŻ details ŮŮ ŘŹŘŻŮŮ Ř§ŮŘŁŘŹŮŘ˛ŘŠ ŘšŮŘŻ ŮŮ ŮŘŞŘ­
    await _ensureAllDevicesColumns(db);
    await _ensureAllSubscriptionsColumns(db);
    return db;
  }

  // ŘŻŘ§ŮŘŠ ŮŮŘ­Řľ ŮŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ details ŮŮ ŘŹŘŻŮŮ Ř§ŮŘŁŘŹŮŘ˛ŘŠ ŘĽŘ°Ř§ ŮŮ ŮŮŮ ŮŮŘŹŮŘŻŮŘ§
  Future<void> _ensureDetailsColumnInDevices(Database db) async {
    try {
      final columns = await db.rawQuery("PRAGMA table_info(devices)");
      final hasDetails = columns.any((c) => c['name'] == 'details');
      if (!hasDetails) {
        print(
          '[DB] ŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ details ŘĽŮŮ ŘŹŘŻŮŮ Ř§ŮŘŁŘŹŮŘ˛ŘŠ ŘŞŮŮŘ§ŘŚŮŮŘ§',
        );
        await db.execute('ALTER TABLE devices ADD COLUMN details TEXT;');
      }
    } catch (e) {
      print('[DB] ŘŽŘˇŘŁ ŘŁŘŤŮŘ§ŘĄ Ř§ŮŘŞŘŁŮŘŻ ŮŮ ŘšŮŮŘŻ details: ');
      print(e);
    }
  }

  // ŘŻŘ§ŮŘŠ ŮŮŘ­Řľ ŮŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ active ŮŮ ŘŹŘŻŮŮ boards ŘĽŘ°Ř§ ŮŮ ŮŮŮ ŮŮŘŹŮŘŻŮŘ§
  Future<void> _ensureActiveColumnInBoards(Database db) async {
    try {
      final columns = await db.rawQuery("PRAGMA table_info(boards)");
      final hasActive = columns.any((c) => c['name'] == 'active');
      if (!hasActive) {
        print(
          '[DB] ŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ active ŘĽŮŮ ŘŹŘŻŮŮ boards ŘŞŮŮŘ§ŘŚŮŮŘ§',
        );
        await db.execute(
          'ALTER TABLE boards ADD COLUMN active INTEGER NOT NULL DEFAULT 1;',
        );
      }
    } catch (e) {
      print('[DB] ŘŽŘˇŘŁ ŘŁŘŤŮŘ§ŘĄ Ř§ŮŘŞŘŁŮŘŻ ŮŮ ŘšŮŮŘŻ active: ');
      print(e);
    }
  }

  Future _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE subscribers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        remoteId INTEGER, -- ŮŘšŘąŮ Ř§ŮŮŘ´ŘŞŘąŮ ŮŮ Ř§ŮŮŮŮŘš
        name TEXT NOT NULL,
        totalDebt REAL NOT NULL,
        user TEXT NOT NULL,
        subscriptionPrice REAL NOT NULL,
        subscriptionType TEXT NOT NULL,
        startDate TEXT NOT NULL,
        endDate TEXT NOT NULL,
        phone TEXT NOT NULL,
        notes TEXT,
        ip TEXT,
        subscriptionId TEXT,
        buyPrice REAL,
        status TEXT,
        contract INTEGER DEFAULT 0,
        online_status INTEGER DEFAULT 0,
        boardId INTEGER,
        isDeleted INTEGER DEFAULT 0,
        custom_ip INTEGER DEFAULT 0,
        source_type TEXT DEFAULT 'server'
      )
    ''');
    await db.execute('''
      CREATE TABLE transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type INTEGER NOT NULL,
        description TEXT NOT NULL,
        date TEXT NOT NULL,
        subscriberId INTEGER,
        FOREIGN KEY(subscriberId) REFERENCES subscribers(id)
      )
    ''');
    await db.execute('''
      CREATE TABLE subscriptions (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        buyPrice REAL NOT NULL,
        sellPrice REAL NOT NULL
      )
    ''');
    await db.execute('''
      CREATE TABLE IF NOT EXISTS servers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        ip TEXT NOT NULL,
        user TEXT NOT NULL,
        pass TEXT NOT NULL,
        connected INTEGER NOT NULL DEFAULT 0
      )
    ''');
    await db.execute('''
      CREATE TABLE IF NOT EXISTS boards (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        user TEXT NOT NULL,
        pass TEXT NOT NULL,
        token TEXT,
        connected INTEGER NOT NULL DEFAULT 0
      )
    ''');
    await db.execute('''
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT
      )
    ''');
    await db.execute('''
      CREATE TABLE IF NOT EXISTS devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ip TEXT NOT NULL,
        user TEXT NOT NULL,
        pass TEXT NOT NULL,
        name TEXT,
        type TEXT,
        connected INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  Future _upgradeDB(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await db.execute('ALTER TABLE subscribers ADD COLUMN ip TEXT;');
    }
    // ŘĽŘśŘ§ŮŘŠ ŘŁŘšŮŘŻŘŠ ŘŹŘŻŮŘŻŘŠ ŘĽŘ°Ř§ ŮŮ ŘŞŮŮ ŮŮŘŹŮŘŻŘŠ
    final subColumns = await db.rawQuery("PRAGMA table_info(subscribers)");
    // remoteId
    final hasRemoteId = subColumns.any((c) => c['name'] == 'remoteId');
    if (!hasRemoteId) {
      await db.execute('ALTER TABLE subscribers ADD COLUMN remoteId INTEGER;');
      print(
        '[DB] ŘŞŮ ŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ remoteId ŘĽŮŮ ŘŹŘŻŮŮ subscribers',
      );
    }
    // contract
    final hasContract = subColumns.any((c) => c['name'] == 'contract');
    if (!hasContract) {
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN contract INTEGER DEFAULT 0;',
      );
      print(
        '[DB] ŘŞŮ ŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ contract ŘĽŮŮ ŘŹŘŻŮŮ subscribers',
      );
    }
    // boardId
    final hasBoardId = subColumns.any((c) => c['name'] == 'boardId');
    if (!hasBoardId) {
      await db.execute('ALTER TABLE subscribers ADD COLUMN boardId INTEGER;');
      print(
        '[DB] ŘŞŮ ŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ boardId ŘĽŮŮ ŘŹŘŻŮŮ subscribers',
      );
    }
    // ŘŞŘŁŮŘŻ ŮŮ ŮŘŹŮŘŻ ŘšŮŮŘŻ active ŮŮ ŘŹŘŻŮŮ boards
    final columns = await db.rawQuery("PRAGMA table_info(boards)");
    final hasActive = columns.any((c) => c['name'] == 'active');
    if (!hasActive) {
      await db.execute(
        'ALTER TABLE boards ADD COLUMN active INTEGER NOT NULL DEFAULT 1;',
      );
    }
    // Ř§ŮŘŁŘšŮŘŻŘŠ Ř§ŮŘŁŘŽŘąŮ (subscriptionId, buyPrice, status)
    try {
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN subscriptionId TEXT;',
      );
    } catch (e) {}
    try {
      await db.execute('ALTER TABLE subscribers ADD COLUMN buyPrice REAL;');
    } catch (e) {}
    try {
      await db.execute('ALTER TABLE subscribers ADD COLUMN status TEXT;');
    } catch (e) {}
    // online_status
    final hasOnlineStatus = subColumns.any((c) => c['name'] == 'online_status');
    if (!hasOnlineStatus) {
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN online_status INTEGER DEFAULT 0;',
      );
      print(
        '[DB] ŘŞŮ ŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ online_status ŘĽŮŮ ŘŹŘŻŮŮ subscribers',
      );
    }
    // isDeleted
    final hasIsDeleted = subColumns.any((c) => c['name'] == 'isDeleted');
    if (!hasIsDeleted) {
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN isDeleted INTEGER DEFAULT 0;',
      );
      print(
        '[DB] ŘŞŮ ŘĽŘśŘ§ŮŘŠ ŘšŮŮŘŻ isDeleted ŘĽŮŮ ŘŹŘŻŮŮ subscribers',
      );
    }
    // custom_ip
    final hasCustomIP = subColumns.any((c) => c['name'] == 'custom_ip');
    if (!hasCustomIP) {
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN custom_ip INTEGER DEFAULT 0;',
      );
      print('[DB] تم إضافة عمود custom_ip إلى جدول subscribers');
    }

    // source_type
    final hasSourceType = subColumns.any((c) => c['name'] == 'source_type');
    if (!hasSourceType) {
      await db.execute(
        'ALTER TABLE subscribers ADD COLUMN source_type TEXT DEFAULT "server";',
      );
      print('[DB] تم إضافة عمود source_type إلى جدول subscribers');
    }

    // secondary_ip
    final hasSecondaryIP = subColumns.any((c) => c['name'] == 'secondary_ip');
    if (!hasSecondaryIP) {
      await db.execute('ALTER TABLE subscribers ADD COLUMN secondary_ip TEXT;');
      print('[DB] تم إضافة عمود secondary_ip إلى جدول subscribers');
    }
    // Ř§ŮŘŞŘŁŮŘŻ ŮŮ ŮŘŹŮŘŻ ŘŹŘŻŮŮ settings
    try {
      await db.execute('''
        CREATE TABLE IF NOT EXISTS settings (
          key TEXT PRIMARY KEY,
          value TEXT
        )
      ''');
    } catch (e) {}
  }

  Future<List<Subscriber>> getAllSubscribers({int? boardId}) async {
    final db = await instance.database;
    List<Map<String, dynamic>> result;
    if (boardId != null) {
      // جلب المشتركين من اللوحة المحددة + المشتركين اليدويين (source_type = 'manual')
      result = await db.query(
        'subscribers',
        where: 'boardId = ? OR source_type = ?',
        whereArgs: [boardId, 'manual'],
      );
    } else {
      result = await db.query('subscribers');
    }
    print(
      '[DB][getAllSubscribers] ŘšŘŻŘŻ Ř§ŮŮŘ´ŘŞŘąŮŮŮ Ř§ŮŮŘšŘ§ŘŻŮŮ: [32m${result.length}[0m boardId=$boardId',
    );
    return result.map((map) => Subscriber.fromMap(map)).toList();
  }

  Future<int> insertSubscriber(Subscriber sub) async {
    final db = await instance.database;
    final res = await db.insert('subscribers', sub.toMap());
    print('[DB][insertSubscriber] ŘŞŮ ŘĽŘŻŘąŘ§ŘŹ ŮŘ´ŘŞŘąŮ: ${sub.toMap()}');
    return res;
  }

  Future<int> deleteSubscriber(int id) async {
    final db = await instance.database;
    print('[DB][deleteSubscriber] Ř­Ř°Ů ŮŘ´ŘŞŘąŮ id=$id');
    return await db.delete('subscribers', where: 'id = ?', whereArgs: [id]);
  }

  Future<int> updateSubscriber(Subscriber sub) async {
    final db = await instance.database;
    print('[DB][updateSubscriber] ŘŞŘ­ŘŻŮŘŤ ŮŘ´ŘŞŘąŮ: ${sub.toMap()}');
    return await db.update(
      'subscribers',
      sub.toMap(),
      where: 'id = ?',
      whereArgs: [sub.id],
    );
  }

  Future<int> insertTransaction(app_transaction.Transaction t) async {
    final db = await instance.database;
    return await db.insert('transactions', t.toMap());
  }

  Future<List<Map<String, dynamic>>> getAllSubscriptions() async {
    final db = await instance.database;
    return await db.query('subscriptions');
  }

  Future<int> insertSubscription(Map<String, dynamic> sub) async {
    final db = await instance.database;
    return await db.insert('subscriptions', sub);
  }

  Future<int> updateSubscription(Map<String, dynamic> sub) async {
    final db = await instance.database;
    return await db.update(
      'subscriptions',
      sub,
      where: 'id = ?',
      whereArgs: [sub['id']],
    );
  }

  /// إدراج أو تحديث باقة (للمزامنة)
  Future<int> insertOrUpdateSubscription(Map<String, dynamic> sub) async {
    final db = await instance.database;

    // التحقق من وجود الباقة
    final existing = await db.query(
      'subscriptions',
      where: 'id = ?',
      whereArgs: [sub['id']],
      limit: 1,
    );

    if (existing.isNotEmpty) {
      // تحديث الباقة الموجودة (الحفاظ على الأسعار المخصصة)
      final existingData = existing.first;
      final updateData = Map<String, dynamic>.from(sub);

      // الحفاظ على الأسعار المخصصة إذا كانت موجودة
      if (existingData['custom_buy_price'] != null) {
        updateData['custom_buy_price'] = existingData['custom_buy_price'];
      }
      if (existingData['custom_sell_price'] != null) {
        updateData['custom_sell_price'] = existingData['custom_sell_price'];
      }

      return await db.update(
        'subscriptions',
        updateData,
        where: 'id = ?',
        whereArgs: [sub['id']],
      );
    } else {
      // إدراج باقة جديدة
      return await db.insert('subscriptions', sub);
    }
  }

  Future<int> deleteSubscription(String id) async {
    final db = await instance.database;
    return await db.delete('subscriptions', where: 'id = ?', whereArgs: [id]);
  }

  Future<void> createServersTableIfNotExists() async {
    final db = await instance.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS servers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        ip TEXT NOT NULL,
        user TEXT NOT NULL,
        pass TEXT NOT NULL,
        connected INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  Future<List<Map<String, dynamic>>> getAllServers() async {
    final db = await instance.database;
    return await db.query('servers');
  }

  Future<int> insertServer(Map<String, dynamic> server) async {
    final db = await instance.database;
    return await db.insert('servers', server);
  }

  Future<int> updateServer(int id, Map<String, dynamic> server) async {
    final db = await instance.database;
    return await db.update('servers', server, where: 'id = ?', whereArgs: [id]);
  }

  Future<int> deleteServer(int id) async {
    final db = await instance.database;
    return await db.delete('servers', where: 'id = ?', whereArgs: [id]);
  }

  Future<void> createBoardsTableIfNotExists() async {
    final db = await instance.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS boards (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        user TEXT NOT NULL,
        pass TEXT NOT NULL,
        token TEXT,
        connected INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  Future<List<Map<String, dynamic>>> getAllBoards() async {
    final db = await instance.database;
    return await db.query('boards');
  }

  Future<int> insertBoard(Map<String, dynamic> board) async {
    final db = await instance.database;
    return await db.insert('boards', board);
  }

  Future<int> updateBoard(int id, Map<String, dynamic> board) async {
    final db = await instance.database;
    return await db.update('boards', board, where: 'id = ?', whereArgs: [id]);
  }

  Future<int> deleteBoard(int id) async {
    final db = await instance.database;
    return await db.delete('boards', where: 'id = ?', whereArgs: [id]);
  }

  // ŘŻŘ§ŮŘŠ ŮŘĽŘşŮŘ§Ů ŮŘ§ŘšŘŻŘŠ Ř§ŮŘ¨ŮŘ§ŮŘ§ŘŞ ŮŘ­Ř°Ů ŮŮŮŮŘ§ ŮŘĽŘšŘ§ŘŻŘŠ ŘŞŮŮŘŚŘŠ Ř§ŮŘ§ŘŞŘľŘ§Ů
  Future<void> resetDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'itower.db');
    await deleteDatabase(path);
  }

  // ŘŻŘ§ŮŘŠ ŮŘĽŘšŘ§ŘŻŘŠ ŘŞŮŮŘŚŘŠ Ř§ŮŘ§ŘŞŘľŘ§Ů Ř¨ŮŘ§ŘšŘŻŘŠ Ř§ŮŘ¨ŮŘ§ŮŘ§ŘŞ Ř¨ŘšŘŻ Ř§ŮŘ­Ř°Ů ŘŁŮ Ř§ŮŘĽŘşŮŘ§Ů
  Future<void> reopenDatabase() async {
    _database = null;
    await database;
  }

  // ŘĽŘšŘŻŘ§ŘŻ Ř§ŮŘŞŘąŘ§ŘśŮ: 0 = Ř­ŘłŘ¨ ŮŘŻŘŠ Ř§ŮŘ´ŮŘąŘ 1 = 30 ŮŮŮ ŘŤŘ§Ř¨ŘŞŘŠ
  static const int defaultDurationType = 0;

  // ŘĽŘšŘŻŘ§ŘŻ ŮŮŘš ŮŘŻŘŠ Ř§ŮŘ§Ř´ŘŞŘąŘ§Ů ŮŮ ŮŘ§ŘšŘŻŘŠ Ř§ŮŘ¨ŮŘ§ŮŘ§ŘŞ (ŘŹŘŻŮŮ ŘĽŘšŘŻŘ§ŘŻŘ§ŘŞ)
  Future<void> setDurationType(int type) async {
    final db = await instance.database;
    await db.insert('settings', {
      'key': 'durationType',
      'value': type,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  // ŘŹŮŘ¨ ŮŮŘš ŮŘŻŘŠ Ř§ŮŘ§Ř´ŘŞŘąŘ§Ů ŮŮ ŮŘ§ŘšŘŻŘŠ Ř§ŮŘ¨ŮŘ§ŮŘ§ŘŞ
  Future<int> getDurationType() async {
    final db = await instance.database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: ['durationType'],
      limit: 1,
    );
    if (result.isNotEmpty) {
      return int.tryParse(result.first['value'].toString()) ??
          defaultDurationType;
    }
    return defaultDurationType;
  }

  // ŘŻŘ§ŮŘŠ ŘšŘ§ŮŘŠ ŮŘ­ŮŘ¸ ŘŁŮ ŘĽŘšŘŻŘ§ŘŻ ŮŘľŮ ŮŮ ŘŹŘŻŮŮ settings
  Future<void> setSetting(String key, String value) async {
    final db = await instance.database;
    await db.insert('settings', {
      'key': key,
      'value': value,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  // ŘŹŮŘ¨ ŮŘľ ŘąŘłŘ§ŮŘŠ ŮŮ ŮŘ§ŘšŘŻŘŠ Ř§ŮŘ¨ŮŘ§ŮŘ§ŘŞ
  Future<String> getMessage(String key, String defaultValue) async {
    final db = await instance.database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
      limit: 1,
    );
    if (result.isNotEmpty) {
      final val = result.first['value'];
      if (val is String) return val;
      if (val != null) return val.toString();
    }
    return defaultValue;
  }

  // Ř­ŮŘ¸ ŮŘľ ŘąŘłŘ§ŮŘŠ ŮŮ ŮŘ§ŘšŘŻŘŠ Ř§ŮŘ¨ŮŘ§ŮŘ§ŘŞ
  Future<void> setMessage(String key, String value) async {
    final db = await instance.database;
    await db.insert('settings', {
      'key': key,
      'value': value,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  // ŘŹŮŘ¨ Ř§ŮŮŘľ Ř§ŮŘ§ŮŘŞŘąŘ§ŘśŮ ŮŮŘˇ (Ř¨ŘŻŮŮ ŘŞŘŽŘľŮŘľ Ř§ŮŮŘłŘŞŘŽŘŻŮ)
  Future<String> getDefaultMessage(String key) async {
    switch (key) {
      case 'start_msg':
        return 'ŘŞŘ­ŮŘŠ ŘˇŮŘ¨ŘŠŘ Ř§Ř´ŘŞŘąŘ§Ů Ř§ŮŘ§ŮŘŞŘąŮŘŞ Ř§ŮŘŽŘ§Řľ Ř¨Ů ŘŞŮ ŘŞŘŹŘŻŮŘŻŮ Ř¨ŮŘŹŘ§Ř­. ŘłŘšŘą Ř§ŮŘ§Ř´ŘŞŘąŘ§Ů: {ŘłŘšŘą_Ř§ŮŘ§Ř´ŘŞŘąŘ§Ů} ŮŘ§ŮŘŻŮŮ Ř§ŮŘ§ŘŹŮŘ§ŮŮ ŮŮ: {Ř§ŮŘŻŮŮ}. ŘŞŘ§ŘąŮŘŽ Ř§ŮŘŞŮŘ§ŘĄ Ř§ŮŘ§Ř´ŘŞŘąŘ§Ů ŮŮ: {ŘŞŘ§ŘąŮŘŽ_Ř§ŮŘ§ŮŘŞŮŘ§ŘĄ}. Ř´ŮŘąŘ§Ů ŮŘ§ŘŽŘŞŮŘ§ŘąŮ ŮŘŽŘŻŮŘ§ŘŞŮŘ§';
      case 'near_end_msg':
        return 'ŮŘąŘ­Ř¨Ř§ŮŘ ŮŮŘŻ Ř§ŘšŮŘ§ŮŮ Ř¨ŘŁŮ Ř§ŮŘ§Ř´ŘŞŘąŘ§Ů Ř§ŮŘŽŘ§Řľ Ř¨Ů ŮŮ Ř§ŮŘ§ŮŘŞŘąŮŘŞ ŘłŮŮŘŞŮŮ ŮŮ ŘŞŘ§ŘąŮŘŽ {ŘŞŘ§ŘąŮŘŽ_Ř§ŮŘ§ŮŘŞŮŘ§ŘĄ}. ŮŘ§ ŮŘ¨ŮŮ ŘłŮŮ ŮŮŮ ŮŘ§Ř­ŘŻ. ŮŮŘ­ŮŘ§Ř¸ ŘšŮŮ Ř§ŘłŘŞŮŘąŘ§ŘąŮŘŠ ŘŽŘŻŮŘŠ Ř§ŮŘ§ŮŘŞŘąŮŘŞ ŮŘŻŮŮŘ ŮŘąŘŹŮ Ř§ŮŘŞŘŹŘŻŮŘŻ. Ř´ŮŘąŘ§Ů ŮŮ.';
      case 'pay_msg':
        return 'ŮŘąŘ­Ř¨Ř§ŮŘ ŘŞŮ Ř§ŘłŘŞŮŘ§Ů ŘŻŮŘšŘŞŮ Ř¨ŮŘ¨ŮŘş {Ř§ŮŮŘ¨ŮŘş_Ř§ŮŮŘłŘŻŘŻ} Ů Ř§ŮŘŻŮŮ Ř§ŮŮŘŞŘ¨ŮŮ ŮŮ {Ř§ŮŘŻŮŮ}.';
      case 'end_msg':
        return 'ŘąŘŹŘ§ŘĄŮŘ ŮŮŘŻ ŘĽŘ¨ŮŘ§ŘşŮ Ř¨ŘŁŮ Ř§Ř´ŘŞŘąŘ§Ů Ř§ŮŘ§ŮŘŞŘąŮŘŞ Ř§ŮŘŽŘ§Řľ Ř¨Ů ŮŘŻ Ř§ŮŘŞŮŮ.';
      default:
        return '';
    }
  }

  /// ŘŻŘ§ŮŘŠ ŘŞŘŞŘ­ŮŮ ŘĽŘ°Ř§ ŮŘ§Ů ŮŮŘ§Ů ŮŘ´ŘŞŘąŮ Ř¨ŮŮŘł Ř§ŮŮŮŘ˛Řą
  Future<bool> isUserExists(String user, {int? excludeId}) async {
    final db = await instance.database;
    List<Map<String, dynamic>> result;
    if (excludeId != null) {
      result = await db.query(
        'subscribers',
        where: 'user = ? AND id != ?',
        whereArgs: [user, excludeId],
        limit: 1,
      );
    } else {
      result = await db.query(
        'subscribers',
        where: 'user = ?',
        whereArgs: [user],
        limit: 1,
      );
    }
    return result.isNotEmpty;
  }

  Future<Map<String, dynamic>?> getActiveBoard() async {
    final db = await instance.database;
    final boards = await db.query('boards', where: 'active = 1', limit: 1);
    if (boards.isNotEmpty) return boards.first;
    return null;
  }

  Future<Subscriber?> getSubscriberById(int id) async {
    final db = await instance.database;
    final result = await db.query(
      'subscribers',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (result.isNotEmpty) {
      return Subscriber.fromMap(result.first);
    }
    return null;
  }

  Future<Map<String, dynamic>?> getSubscriptionById(String id) async {
    final db = await instance.database;
    final result = await db.query(
      'subscriptions',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (result.isNotEmpty) {
      return result.first;
    }
    return null;
  }
}

// ŮŮ db_helper.dart
// ŘĽŘśŘ§ŮŘŠ ŘŻŮŘ§Ů Ř§Ř­ŘŞŘąŘ§ŮŮŘŠ ŮŘĽŘŻŘąŘ§ŘŹ ŘŁŮ ŘŞŘ­ŘŻŮŘŤ Ř§ŮŘ¨Ř§ŮŘŠ
extension PackageSubscriberDB on DBHelper {
  Future<void> insertOrUpdatePackage(Map<String, dynamic> pkg) async {
    final db = await DBHelper.instance.database;
    final id = pkg['id'];
    final existing = await db.query(
      'subscriptions',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    // ŮŮŘŞŘąŘŠ Ř§ŮŘ­ŮŮŮ ŮŘŞŘŞŮŘ§ŮŮ ŮŘš Ř§ŮŘŹŘŻŮŮ
    final filteredPkg = <String, dynamic>{
      'id': pkg['id'],
      'name': pkg['name'],
      'buyPrice': pkg['buyPrice'],
      'sellPrice': pkg['sellPrice'],
    };
    // ŘŞŘ­ŮŮ ŮŮ Ř§ŮŘ­ŮŮŮ Ř§ŮŘŁŘłŘ§ŘłŮŘŠ
    if (filteredPkg.values.any((v) => v == null)) {
      print('[SYNC][ERROR] Ř­ŮŮ ŮŘ§ŮŘľ ŮŮ Ř§ŮŘ¨Ř§ŮŘŠ: $filteredPkg');
      return;
    }
    if (existing.isNotEmpty) {
      await db.update(
        'subscriptions',
        filteredPkg,
        where: 'id = ?',
        whereArgs: [id],
      );
    } else {
      await db.insert('subscriptions', filteredPkg);
    }
  }

  Future<void> insertOrUpdateSubscriber(
    Map<String, dynamic> user, {
    int? boardId,
  }) async {
    final db = await DBHelper.instance.database;
    final username = user['user'];
    final existing = await db.query(
      'subscribers',
      where: 'user = ? AND boardId = ?',
      whereArgs: [username, boardId],
      limit: 1,
    );
    print(
      '[SYNC][insertOrUpdateSubscriber] user=$username, boardId=$boardId, ŮŮŘŹŮŘŻŘ ${existing.isNotEmpty}',
    );
    double? localDebt;
    if (existing.isNotEmpty) {
      localDebt = existing.first['totalDebt'] is num
          ? (existing.first['totalDebt'] as num).toDouble()
          : 0.0;
    }
    final filteredUser = <String, dynamic>{
      'name': user['name'],
      'totalDebt': localDebt ?? 0.0,
      'user': user['user'],
      'subscriptionPrice': user['subscriptionPrice'],
      'subscriptionType': user['subscriptionType'],
      'startDate': user['startDate'],
      'endDate': user['endDate'],
      'phone': user['phone'],
      'notes': user['notes'],
      'ip': user['ip'],
      'subscriptionId': user['subscriptionId'],
      'buyPrice': user['buyPrice'],
      'remoteId': user['remoteId'],
      'status': user['status'],
      'contract': user['contract'] ?? _calculateContract(user),
      'boardId': boardId,
      'online_status': user['online_status'] ?? 0,
      'isDeleted': user['isDeleted'] ?? 0,
    };
    print('[SYNC][insertOrUpdateSubscriber] filteredUser=$filteredUser');
    final requiredFields = [
      'name',
      'totalDebt',
      'user',
      'subscriptionPrice',
      'subscriptionType',
      'startDate',
      'endDate',
      'phone',
    ];
    for (final field in requiredFields) {
      if (filteredUser[field] == null) {
        print(
          '[SYNC][ERROR] Ř­ŮŮ ŮŘ§ŮŘľ ŮŮ Ř§ŮŮŘ´ŘŞŘąŮ: $field, Ř§ŮŘ¨ŮŘ§ŮŘ§ŘŞ: $filteredUser',
        );
        return;
      }
    }
    if (existing.isNotEmpty) {
      final id = existing.first['id'];
      print('[SYNC][insertOrUpdateSubscriber] ŘŞŘ­ŘŻŮŘŤ ŮŘ´ŘŞŘąŮ id=$id');
      await db.update(
        'subscribers',
        filteredUser,
        where: 'id = ?',
        whereArgs: [id],
      );
      final afterUpdate = await db.query(
        'subscribers',
        where: 'boardId = ?',
        whereArgs: [boardId],
      );
      print(
        '[DB][insertOrUpdateSubscriber][update] ŘšŘŻŘŻ Ř§ŮŮŘ´ŘŞŘąŮŮŮ Ř¨ŘšŘŻ Ř§ŮŘŞŘ­ŘŻŮŘŤ: [34m${afterUpdate.length}[0m boardId=$boardId',
      );
    } else {
      print('[SYNC][insertOrUpdateSubscriber] ŘĽŘŻŘąŘ§ŘŹ ŮŘ´ŘŞŘąŮ ŘŹŘŻŮŘŻ');
      await db.insert('subscribers', filteredUser);
      final afterInsert = await db.query(
        'subscribers',
        where: 'boardId = ?',
        whereArgs: [boardId],
      );
      print(
        '[DB][insertOrUpdateSubscriber][insert] ŘšŘŻŘŻ Ř§ŮŮŘ´ŘŞŘąŮŮŮ Ř¨ŘšŘŻ Ř§ŮŘĽŘŻŘąŘ§ŘŹ: [36m${afterInsert.length}[0m boardId=$boardId',
      );
    }
  }

  // ŘŻŘ§ŮŘŠ ŮŘłŘ§ŘšŘŻŘŠ ŮŘ­ŘłŘ§Ř¨ contract ŘĽŘ°Ř§ ŮŮ ŮŮŮ ŮŮŘŹŮŘŻŘ§Ů
  int _calculateContract(Map<String, dynamic> user) {
    // ŘĽŘ°Ř§ ŮŘ§Ů ŘŞŘ§ŘąŮŘŽ Ř§ŮŘ§ŮŘŞŮŘ§ŘĄ Ř¨ŘšŘŻ Ř§ŮŮŮŮ ŮŘšŘŞŘ¨Řą ŮŘšŘ§Ů
    try {
      final endDate = DateTime.parse(user['endDate'] ?? '');
      return endDate.isAfter(DateTime.now()) ? 1 : 0;
    } catch (_) {
      return 0;
    }
  }

  // حفظ النص الافتراضي في قاعدة البيانات
  Future<void> setDefaultMessage(String key, String value) async {
    final defaultKey = '${key}_default';
    final db = await DBHelper.instance.database;
    await db.insert('settings', {
      'key': defaultKey,
      'value': value,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  // جلب النص الافتراضي الأصلي من قاعدة البيانات
  Future<String> getOriginalDefaultMessage(String key) async {
    final defaultKey = '${key}_default';
    final db = await DBHelper.instance.database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [defaultKey],
      limit: 1,
    );

    if (result.isNotEmpty) {
      final val = result.first['value'];
      if (val is String) return val;
      if (val != null) return val.toString();
    }

    // إذا لم يوجد النص الافتراضي في قاعدة البيانات، أنشئه وحفظه
    final defaultText = _getHardcodedDefaultMessage(key);
    await setDefaultMessage(key, defaultText);
    return defaultText;
  }

  // النصوص الافتراضية المبرمجة (تستخدم فقط عند الإنشاء الأول)
  String _getHardcodedDefaultMessage(String key) {
    switch (key) {
      case 'start_msg':
        return 'تحية طيبة، اشتراك الانترنت الخاص بك تم تجديده بنجاح. سعر الاشتراك: {سعر_الاشتراك} والدين الاجمالي هو: {الدين}. تاريخ انتهاء الاشتراك هو: {تاريخ_الانتهاء}. شكراً لاختيارك لخدماتنا';
      case 'near_end_msg':
        return 'مرحباً نود اعلامك بأن الاشتراك الخاص بك في الانترنت سينتهي في تاريخ {تاريخ_الانتهاء}. ما عليك سوى دفع واحد. للحفاظ على استمرارية خدمة الانترنت لديك يرجى التجديد. شكراً لك.';
      case 'pay_msg':
        return 'مرحباً تم استلام دفعتك بمبلغ {المبلغ_المسدد} و الدين المتبقي هو {الدين}.';
      case 'end_msg':
        return 'رجاءاً نود إبلاغك بأن اشتراك الانترنت الخاص بك قد انتهى.';
      case 'rem_msg':
        return 'عزيزي {الاسم}، يرجى تسديد مبلغ الديون {الدين}.';
      default:
        return '';
    }
  }
}
