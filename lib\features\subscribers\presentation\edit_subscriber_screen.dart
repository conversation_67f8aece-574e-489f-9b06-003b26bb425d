import 'package:flutter/material.dart';
import '../data/subscriber_model.dart';
import '../domain/subscribers_repository.dart';
import '../../../db_helper.dart';

class EditSubscriberScreen extends StatefulWidget {
  final Subscriber subscriber;
  final SubscribersRepository repository;
  const EditSubscriberScreen({
    super.key,
    required this.subscriber,
    required this.repository,
  });

  @override
  State<EditSubscriberScreen> createState() => _EditSubscriberScreenState();
}

class _EditSubscriberScreenState extends State<EditSubscriberScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _userController;
  late TextEditingController _priceController;
  late TextEditingController _phoneController;
  late TextEditingController _startDateController;
  late String _subscriptionType;
  DateTime? _startDate;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.subscriber.name);
    _userController = TextEditingController(text: widget.subscriber.user);
    _priceController = TextEditingController(
      text: widget.subscriber.subscriptionPrice.toStringAsFixed(0),
    );
    _phoneController = TextEditingController(text: widget.subscriber.phone);
    _startDate = widget.subscriber.startDate;
    _startDateController = TextEditingController(
      text: _formatDateTime(_startDate!),
    );
    _subscriptionType = widget.subscriber.subscriptionType;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _userController.dispose();
    _priceController.dispose();
    _phoneController.dispose();
    _startDateController.dispose();
    super.dispose();
  }

  Future<void> _pickDate() async {
    final now = DateTime.now();
    final picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? now,
      firstDate: DateTime(now.year - 2),
      lastDate: DateTime(now.year + 2),
      locale: const Locale('ar'),
    );
    if (picked != null) {
      final pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_startDate ?? now),
        builder: (context, child) =>
            Directionality(textDirection: TextDirection.rtl, child: child!),
      );
      setState(() {
        _startDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          pickedTime?.hour ?? now.hour,
          pickedTime?.minute ?? now.minute,
        );
        _startDateController.text = _formatDateTime(_startDate!);
      });
    }
  }

  String _formatDateTime(DateTime date) {
    final hour = date.hour % 12 == 0 ? 12 : date.hour % 12;
    final ampm = date.hour >= 12 ? 'AM' : 'PM';
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')} ($hour:${date.minute.toString().padLeft(2, '0')} $ampm)';
  }

  /// دالة احترافية لحساب تاريخ نهاية الاشتراك
  DateTime calculateSubscriptionEndDate(DateTime startDate, int durationType) {
    if (durationType == 0) {
      int year = startDate.year;
      int month = startDate.month + 1;
      if (month > 12) {
        month = 1;
        year += 1;
      }
      int day = startDate.day;
      int lastDayOfNextMonth = DateTime(year, month + 1, 0).day;
      if (day > lastDayOfNextMonth) day = lastDayOfNextMonth;
      return DateTime(
        year,
        month,
        day,
        startDate.hour,
        startDate.minute,
        startDate.second,
      );
    } else {
      return startDate.add(const Duration(days: 30));
    }
  }

  void _save() async {
    if (!_formKey.currentState!.validate() || _startDate == null) return;
    // تحقق من عدم تكرار اليوزر مع استثناء المشترك الحالي
    final userInput = _userController.text.trim();
    final userExists = await DBHelper.instance.isUserExists(
      userInput,
      excludeId: widget.subscriber.id,
    );
    if (userExists) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('اليوزر موجود بالفعل، يرجى اختيار يوزر آخر'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }
    // جلب نوع المدة من قاعدة البيانات
    final durationType = await DBHelper.instance.getDurationType();
    final endDate = calculateSubscriptionEndDate(_startDate!, durationType);
    int remainingDays = endDate.difference(DateTime.now()).inDays;
    if (remainingDays < 0) remainingDays = 0;
    final updated = widget.subscriber.copyWith(
      name: _nameController.text.trim(),
      user: _userController.text.trim(),
      subscriptionPrice: double.tryParse(_priceController.text) ?? 0.0,
      subscriptionType: _subscriptionType,
      startDate: _startDate!,
      endDate: endDate,
      phone: _phoneController.text.trim(),
      boardId: widget.subscriber.boardId, // Ensure boardId is included
    );
    await widget.repository.updateSubscriber(updated);
    Navigator.of(context).pop(true);
  }

  /// بناء مؤشر نوع المشترك
  Widget _buildSubscriberTypeIndicator() {
    String typeText;
    Color typeColor;
    IconData typeIcon;

    if (widget.subscriber.isManualSubscriber) {
      typeText = 'مشترك يدوي';
      typeColor = Colors.blue;
      typeIcon = Icons.person_add;
    } else if (widget.subscriber.isSasSubscriber) {
      typeText = 'مشترك SAS';
      typeColor = Colors.green;
      typeIcon = Icons.cloud;
    } else if (widget.subscriber.isEarthlinkSubscriber) {
      typeText = 'مشترك Earthlink';
      typeColor = Colors.orange;
      typeIcon = Icons.language;
    } else {
      typeText = 'مشترك SAS'; // افتراضي للمشتركين القدامى
      typeColor = Colors.green;
      typeIcon = Icons.cloud;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: typeColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(typeIcon, color: typeColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  typeText,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: typeColor,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.subscriber.isManualSubscriber
                      ? 'يمكن تعديل جميع بيانات هذا المشترك محلياً'
                      : 'هذا المشترك مُدار من خلال السيرفر',
                  style: TextStyle(
                    color: typeColor.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final iconColor = Theme.of(context).iconTheme.color;
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل مشترك'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Padding(
          padding: const EdgeInsets.all(18.0),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                // مؤشر نوع المشترك
                _buildSubscriberTypeIndicator(),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _nameController,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                        decoration: InputDecoration(
                          labelText: 'الاسم',
                          labelStyle: TextStyle(
                            color: Theme.of(context).hintColor,
                          ),
                          prefixIcon: Icon(Icons.person, color: iconColor),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Theme.of(context).cardColor,
                        ),
                        validator: (v) =>
                            v == null || v.isEmpty ? 'الاسم مطلوب' : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 14),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _userController,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                        decoration: InputDecoration(
                          labelText: 'اليوزر',
                          labelStyle: TextStyle(
                            color: Theme.of(context).hintColor,
                          ),
                          prefixIcon: Icon(
                            Icons.account_circle,
                            color: iconColor,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Theme.of(context).cardColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 14),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _priceController,
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                        decoration: InputDecoration(
                          labelText: 'سعر الاشتراك',
                          labelStyle: TextStyle(
                            color: Theme.of(context).hintColor,
                          ),
                          prefixIcon: Icon(
                            Icons.attach_money,
                            color: iconColor,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Theme.of(context).cardColor,
                        ),
                        validator: (v) => v == null || v.isEmpty
                            ? 'سعر الاشتراك مطلوب'
                            : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 14),
                TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    labelText: 'رقم الهاتف',
                    labelStyle: TextStyle(color: Theme.of(context).hintColor),
                    prefixIcon: Icon(Icons.phone, color: iconColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                  ),
                  validator: (v) {
                    final phone = v?.replaceAll(RegExp(r'[^0-9]'), '') ?? '';
                    if (phone.isNotEmpty && phone.length != 11) {
                      return 'رقم الهاتف يجب أن يكون 11 رقم بدون رموز أو فواصل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 14),
                TextFormField(
                  readOnly: true,
                  controller: _startDateController,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    labelText: 'تاريخ بدء الاشتراك',
                    labelStyle: TextStyle(color: Theme.of(context).hintColor),
                    prefixIcon: Icon(Icons.calendar_month, color: iconColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                  ),
                  onTap: _pickDate,
                ),

                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _save,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: const Text('تعديل', style: TextStyle(fontSize: 18)),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
