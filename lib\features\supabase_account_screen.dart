import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import '../Backup_Restore_Screen.dart';
import '../settings_page.dart';
import 'activation_options_screen.dart';
import '../services/account_service.dart';
import 'package:device_info_plus/device_info_plus.dart';

class SupabaseAccountScreen extends StatefulWidget {
  final File? profileImageFile;
  final Function(File?) onImageChanged;

  const SupabaseAccountScreen({
    Key? key,
    this.profileImageFile,
    required this.onImageChanged,
  }) : super(key: key);

  @override
  State<SupabaseAccountScreen> createState() => _SupabaseAccountScreenState();
}

class _SupabaseAccountScreenState extends State<SupabaseAccountScreen>
    with TickerProviderStateMixin {
  String userName = '';
  bool isTrial = true;
  String packageText = '';
  String expiryText = '';
  int daysLeft = 0;
  bool isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // متغيرات التفعيل الإضافية
  String activePackage = '';
  DateTime? activationDate;
  DateTime? expiryDate;
  DateTime? creationDate;
  DateTime? trialStartDate;
  DateTime? trialEndDate;
  bool needsActivation = false;
  bool isNearExpiry = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );
    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
          ),
        );
    _loadAccountData();
    _animationController.forward();
  }

  Future<void> _loadAccountData() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;

      if (user == null) {
        // مستخدم غير مسجل دخول
        userName = 'ضيف';
        isTrial = true;
        packageText = 'غير مسجل';
        expiryText = 'يرجى تسجيل الدخول';
        daysLeft = 0;
        return;
      }

      // جلب بيانات الحساب من Supabase
      final accountData = await AccountService.getAccountData(user.id);

      if (accountData == null) {
        // فحص الجهاز قبل إنشاء حساب جديد
        debugPrint('لا توجد بيانات حساب في Supabase للمستخدم: ${user.id}');

        try {
          // الحصول على معرف الجهاز
          final deviceId = await _getDeviceId();

          // فحص وجود حساب آخر في نفس الجهاز
          final existingUserId =
              await AccountService.getExistingAccountForDevice(deviceId);

          if (existingUserId != null && existingUserId != user.id) {
            // يوجد حساب آخر مرتبط بهذا الجهاز
            debugPrint(
              '⚠️ [ACCOUNT_SCREEN] وجد حساب آخر للجهاز: $existingUserId',
            );

            // تسجيل خروج المستخدم الحالي
            await Supabase.instance.client.auth.signOut();

            // إظهار رسالة للمستخدم
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'يوجد حساب آخر مرتبط بهذا الجهاز. تم تسجيل الخروج.',
                  ),
                  backgroundColor: Colors.red,
                ),
              );

              // العودة لشاشة تسجيل الدخول
              Navigator.of(context).pushReplacementNamed('/login');
            }
            return;
          }

          // إذا لم يوجد حساب آخر، إنشاء حساب جديد
          debugPrint('حساب جديد - إنشاء بيانات افتراضية');
          await _createNewAccount(user);
          return;
        } catch (e) {
          debugPrint('❌ [ACCOUNT_SCREEN] خطأ في فحص الجهاز: $e');

          // في حالة الخطأ، لا ننشئ حساب جديد للأمان
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'خطأ في التحقق من الجهاز. يرجى إعادة تسجيل الدخول.',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }
      }

      // استخراج البيانات من Supabase
      await _extractAccountData(accountData, user);

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الحساب: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  // الحصول على معرف الجهاز الثابت
  Future<String> _getDeviceId() async {
    try {
      // أولاً، محاولة الحصول على المعرف المحفوظ محلياً
      final prefs = await SharedPreferences.getInstance();
      String? savedDeviceId = prefs.getString('permanent_device_id');

      if (savedDeviceId != null && savedDeviceId.isNotEmpty) {
        return savedDeviceId;
      }

      // إذا لم يوجد معرف محفوظ، إنشاء معرف جديد
      final deviceInfo = DeviceInfoPlugin();
      String deviceId = '';

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        if (androidInfo.fingerprint.isNotEmpty) {
          deviceId = androidInfo.fingerprint;
        } else if (androidInfo.id.isNotEmpty) {
          deviceId = androidInfo.id;
        } else {
          deviceId =
              '${androidInfo.brand}_${androidInfo.model}_${androidInfo.device}';
        }
        deviceId = 'android_$deviceId';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        final iosDeviceId = iosInfo.identifierForVendor ?? 'unknown_ios';
        deviceId = 'ios_$iosDeviceId';
      } else {
        deviceId = 'unknown_platform_${DateTime.now().millisecondsSinceEpoch}';
      }

      // حفظ المعرف الجديد محلياً
      await prefs.setString('permanent_device_id', deviceId);

      return deviceId;
    } catch (e) {
      debugPrint('❌ [DEVICE_ID] خطأ في الحصول على معرف الجهاز: $e');
      return 'error_device_id_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // إنشاء حساب جديد
  Future<void> _createNewAccount(User user) async {
    try {
      final displayName =
          user.userMetadata?['display_name'] ??
          user.email?.split('@')[0] ??
          'مستخدم';

      final accountData = await AccountService.createAccount(
        user.id,
        displayName: displayName,
      );
      await _extractAccountData(accountData, user);

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في إنشاء حساب جديد: $e');
      // استخدام بيانات افتراضية في حالة الخطأ
      _setDefaultAccountData(user);
    }
  }

  // استخراج البيانات من Supabase
  Future<void> _extractAccountData(
    Map<String, dynamic> accountData,
    User user,
  ) async {
    try {
      // اسم المستخدم
      userName =
          accountData['display_name'] ??
          user.userMetadata?['display_name'] ??
          user.email?.split('@')[0] ??
          'مستخدم';

      // حفظ اسم المستخدم في SharedPreferences للاستخدام في الشاشة الرئيسية
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_display_name', userName);

      // نوع الحساب
      isTrial = accountData['is_trial'] ?? true;

      // التواريخ
      final expiryMillis = accountData['expiry_millis'] ?? 0;
      final creationMillis = accountData['creation_millis'] ?? 0;
      final activationMillis = accountData['activation_millis'] ?? 0;

      // تحديد التواريخ
      if (creationMillis > 0) {
        creationDate = DateTime.fromMillisecondsSinceEpoch(creationMillis);
      }

      if (expiryMillis > 0) {
        expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryMillis);
      }

      if (activationMillis > 0) {
        activationDate = DateTime.fromMillisecondsSinceEpoch(activationMillis);
      }

      // حساب الأيام المتبقية
      if (expiryDate != null) {
        daysLeft = expiryDate!.difference(DateTime.now()).inDays;
        if (daysLeft < 0) daysLeft = 0;
      }

      // تحديد نوع الحساب والرسائل
      if (isTrial) {
        packageText = 'حساب تجريبي';
        needsActivation = true;
        trialStartDate = creationDate;
        trialEndDate = expiryDate;

        expiryText = daysLeft > 0
            ? '$daysLeft يوم متبقي'
            : 'انتهت الفترة التجريبية';

        isNearExpiry = daysLeft <= 3 && daysLeft > 0;
      } else {
        packageText = 'حساب مفعل';
        needsActivation = false;
        activePackage = accountData['active_package'] ?? 'باقة مميزة';
        packageText = 'باقة $activePackage';

        expiryText = daysLeft > 0
            ? '$daysLeft يوم متبقي'
            : 'انتهت صلاحية التفعيل';

        isNearExpiry = daysLeft <= 7 && daysLeft > 0;
      }

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في استخراج بيانات الحساب: $e');
      _setDefaultAccountData(user);
    }
  }

  // تعيين بيانات افتراضية في حالة الخطأ
  void _setDefaultAccountData(User user) {
    userName = user.email?.split('@')[0] ?? 'مستخدم';
    isTrial = true;
    packageText = 'حساب تجريبي';
    expiryText = 'خطأ في تحميل البيانات';
    daysLeft = 0;
    needsActivation = true;

    if (mounted) {
      setState(() {
        isLoading = false;
      });
    }
  }

  // دالة تنسيق التاريخ بأرقام إنجليزية
  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd - HH:mm', 'en').format(date);
  }

  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image != null) {
        final directory = await getApplicationDocumentsDirectory();
        final fileName = 'profile_image.jpg';
        final savedImage = await File(
          image.path,
        ).copy('${directory.path}/$fileName');

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('profileImagePath', savedImage.path);

        widget.onImageChanged(savedImage);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث صورة الملف الشخصي'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في اختيار الصورة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تحديث الصورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    if (isLoading) {
      return Scaffold(
        backgroundColor: colorScheme.surface,
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Stack(
            children: [
              // خلفية متدرجة عصرية مثل BackupRestoreScreen
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isDark
                        ? [
                            colorScheme.primary.withAlpha(230),
                            colorScheme.surface.withAlpha(217),
                          ]
                        : [colorScheme.primary, colorScheme.surface],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
              SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 24,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // رأس الشاشة
                      _buildHeader(colorScheme, isDark),
                      const SizedBox(height: 32),

                      // قسم معلومات الحساب
                      _buildAccountInfoSection(colorScheme, isDark),
                      const SizedBox(height: 24),

                      // قسم الإجراءات
                      _buildActionsSection(colorScheme, isDark),
                      const SizedBox(height: 32),

                      // قسم الحالة (إذا لزم الأمر)
                      if (needsActivation)
                        _buildStatusSection(colorScheme, isDark),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء رأس الشاشة مثل BackupRestoreScreen
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // صورة شخصية دائرية عصرية
        GestureDetector(
          onTap: _pickImage,
          child: Container(
            margin: const EdgeInsets.only(bottom: 18),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withAlpha(46),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundColor: Colors.white.withAlpha(isDark ? 20 : 46),
              backgroundImage: widget.profileImageFile != null
                  ? FileImage(widget.profileImageFile!)
                  : null,
              child: widget.profileImageFile == null
                  ? Icon(Icons.person, color: colorScheme.primary, size: 54)
                  : null,
            ),
          ),
        ),
        // اسم المستخدم
        Text(
          userName,
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(color: colorScheme.shadow.withAlpha(33), blurRadius: 4),
            ],
          ),
        ),
      ],
    );
  }

  // بناء قسم معلومات الحساب مثل BackupRestoreScreen
  Widget _buildAccountInfoSection(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withAlpha(isDark ? 179 : 237),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.account_circle_rounded,
                  color: colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'معلومات الحساب',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'تفاصيل حسابك والحالة الحالية',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withAlpha(179),
              ),
            ),
            const SizedBox(height: 20),

            // معلومات الحساب
            _buildInfoRow(
              icon: Icons.account_circle,
              label: 'نوع الحساب',
              value: isTrial ? 'تجريبي' : 'مفعل',
              color: isTrial ? Colors.orange : Colors.green,
            ),
            const SizedBox(height: 12),

            // تاريخ البداية (إنشاء أو تفعيل)
            if (isTrial && trialStartDate != null) ...[
              _buildInfoRow(
                icon: Icons.calendar_today,
                label: 'تاريخ الإنشاء',
                value: _formatDate(trialStartDate!),
                color: Colors.blue,
              ),
              const SizedBox(height: 12),
            ] else if (!isTrial && activationDate != null) ...[
              _buildInfoRow(
                icon: Icons.calendar_today,
                label: 'تاريخ التفعيل',
                value: _formatDate(activationDate!),
                color: Colors.green,
              ),
              const SizedBox(height: 12),
            ],

            // تاريخ النهاية
            if (expiryDate != null) ...[
              _buildInfoRow(
                icon: Icons.event,
                label: isTrial
                    ? 'تاريخ انتهاء التجريب'
                    : 'تاريخ انتهاء التفعيل',
                value: _formatDate(expiryDate!),
                color: isNearExpiry ? Colors.red : Colors.orange,
              ),
              const SizedBox(height: 12),
            ],

            // الحالة (الأيام المتبقية)
            _buildInfoRow(
              icon: Icons.schedule,
              label: 'الأيام المتبقية',
              value: daysLeft > 0 ? '$daysLeft يوم' : 'منتهية الصلاحية',
              color: isNearExpiry
                  ? Colors.red
                  : (isTrial ? Colors.orange : Colors.green),
            ),

            // معلومات الباقة للحسابات المفعلة
            if (!isTrial) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                icon: Icons.workspace_premium,
                label: 'الباقة',
                value: activePackage.isNotEmpty ? activePackage : 'باقة مميزة',
                color: Colors.blue,
              ),
            ],
          ],
        ),
      ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(179),
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // بناء قسم الإجراءات مثل BackupRestoreScreen
  Widget _buildActionsSection(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withAlpha(isDark ? 179 : 237),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.settings_rounded,
                  color: colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'الإجراءات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'إدارة حسابك والوصول للميزات',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withAlpha(179),
              ),
            ),
            const SizedBox(height: 20),

            // أزرار الإجراءات
            if (needsActivation) ...[
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.workspace_premium, size: 20),
                      label: const Text('تفعيل الحساب'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ActivationOptionsScreen(
                              onSelect: (type, amount) {
                                debugPrint(
                                  'تم اختيار باقة: $type بمبلغ: $amount',
                                );
                              },
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.backup, size: 20),
                    label: const Text('النسخ الاحتياطي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      foregroundColor: colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const BackupRestoreScreen(),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.settings, size: 20),
                    label: const Text('الإعدادات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.secondary,
                      foregroundColor: colorScheme.onSecondary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SettingsPage(),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // زر تسجيل الخروج
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.logout, size: 20),
                label: const Text('تسجيل الخروج'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                onPressed: _showLogoutDialog,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم الحالة للحسابات التي تحتاج تفعيل
  Widget _buildStatusSection(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: Colors.orange.withAlpha(isDark ? 51 : 76),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 48),
            const SizedBox(height: 16),
            Text(
              'حساب تجريبي',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'قم بتفعيل حسابك للحصول على جميع الميزات',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withAlpha(179),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // بناء محتوى الحساب مثل SettingsPage
  Widget _buildAccountContent(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // قسم معلومات الحساب
        _buildModernSection(
          'معلومات الحساب',
          Icons.info_outline,
          colorScheme,
          isDark,
          [
            _buildInfoListTile(
              icon: Icons.account_circle,
              title: 'نوع الحساب',
              subtitle: isTrial ? 'تجريبي' : 'مفعل',
              color: isTrial ? Colors.orange : Colors.green,
            ),
            _buildInfoListTile(
              icon: Icons.schedule,
              title: 'الحالة',
              subtitle: expiryText,
              color: isNearExpiry
                  ? Colors.red
                  : (isTrial ? Colors.orange : Colors.green),
            ),
            if (!isTrial)
              _buildInfoListTile(
                icon: Icons.workspace_premium,
                title: 'الباقة',
                subtitle: activePackage.isNotEmpty
                    ? activePackage
                    : 'باقة مميزة',
                color: Colors.blue,
              ),
          ],
        ),
        const SizedBox(height: 24),

        // قسم الإجراءات
        _buildModernSection('الإجراءات', Icons.settings, colorScheme, isDark, [
          if (needsActivation)
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green, Colors.green.withValues(alpha: 0.8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.workspace_premium, color: Colors.white),
              ),
              title: const Text('تفعيل الحساب'),
              subtitle: const Text('احصل على الميزات الكاملة'),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ActivationOptionsScreen(
                      onSelect: (type, amount) {
                        debugPrint('تم اختيار باقة: $type بمبلغ: $amount');
                      },
                    ),
                  ),
                );
              },
            ),
          ListTile(
            leading: const Icon(Icons.backup),
            title: const Text('النسخ الاحتياطي'),
            subtitle: const Text('إدارة النسخ الاحتياطية'),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BackupRestoreScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('الإعدادات'),
            subtitle: const Text('إعدادات التطبيق العامة'),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsPage()),
              );
            },
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.red, Colors.red.withValues(alpha: 0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.logout, color: Colors.white),
            ),
            title: const Text('تسجيل الخروج'),
            subtitle: const Text('الخروج من الحساب الحالي'),
            onTap: _showLogoutDialog,
          ),
        ]),
      ],
    );
  }

  // دالة بناء ListTile للمعلومات
  Widget _buildInfoListTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(icon, color: color),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
    );
  }

  // بناء قسم عصري مثل SettingsPage
  Widget _buildModernSection(
    String title,
    IconData icon,
    ColorScheme colorScheme,
    bool isDark,
    List<Widget> children,
  ) {
    return Card(
      elevation: isDark ? 4 : 2,
      color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: colorScheme.primary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  // تم حذف _buildProfileSection - الآن نستخدم _buildHeader

  // تم حذف الدوال القديمة - الآن نستخدم التصميم الجديد

  Widget _buildProfileHeader(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [colorScheme.primary, colorScheme.primary.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipOval(
                child: widget.profileImageFile != null
                    ? Image.file(widget.profileImageFile!, fit: BoxFit.cover)
                    : Container(
                        color: Colors.white,
                        child: Icon(
                          Icons.person,
                          size: 50,
                          color: colorScheme.primary,
                        ),
                      ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            userName,
            style: textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              packageText,
              style: textTheme.bodyMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountInfo(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الحساب',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: 20),
          _buildInfoRow(
            icon: Icons.account_circle,
            label: 'نوع الحساب',
            value: isTrial ? 'تجريبي' : 'مفعل',
            color: isTrial ? Colors.orange : Colors.green,
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            icon: Icons.schedule,
            label: 'الحالة',
            value: expiryText,
            color: isNearExpiry
                ? Colors.red
                : (isTrial ? Colors.orange : Colors.green),
          ),
          if (!isTrial) ...[
            const SizedBox(height: 16),
            _buildInfoRow(
              icon: Icons.workspace_premium,
              label: 'الباقة',
              value: activePackage.isNotEmpty ? activePackage : 'باقة مميزة',
              color: Colors.blue,
            ),
          ],
        ],
      ),
    );
  }

  // دالة بناء معلومة واحدة
  Widget _buildInfoTile({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required ColorScheme colorScheme,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF2A2A2A) : const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? const Color(0xFF404040) : const Color(0xFFE0E0E0),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دالة بناء زر إجراء عصري
  Widget _buildModernActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required ColorScheme colorScheme,
    required VoidCallback onTap,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDark ? const Color(0xFF2A2A2A) : const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isDark ? const Color(0xFF404040) : const Color(0xFFE0E0E0),
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تم حذف _buildInfoRow المكررة - نستخدم الجديدة

  // تم حذف _buildActionButtons القديم - الآن نستخدم _buildActionsSection

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد من أنك تريد تسجيل الخروج؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _logout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _logout() async {
    try {
      // تسجيل الخروج من Supabase
      await Supabase.instance.client.auth.signOut();

      // مسح البيانات المحلية
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      if (mounted) {
        // العودة إلى شاشة تسجيل الدخول
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    } catch (e) {
      debugPrint('خطأ في تسجيل الخروج: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تسجيل الخروج'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
