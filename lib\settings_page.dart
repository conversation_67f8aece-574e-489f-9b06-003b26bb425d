import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'features/main_home_screen.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
import 'features/subscribers/presentation/subscription_prices_screen.dart';
import 'auto_notifications_screen.dart';
import 'db_helper.dart';
import 'features/sources_screen.dart';
import 'privacy_policy_screen.dart';
import 'features/subscribers/presentation/transactions_log_screen.dart';
import 'features/settings/presentation/general_settings_screen.dart';
import 'features/profits/profits_screen.dart';
import 'features/settings/presentation/ping_screen.dart';
import 'services/introduction_service.dart';
import '../Backup_Restore_Screen.dart';
import 'update_password_screen.dart';
import 'services/supabase_auth_service.dart';

class SettingsPage extends StatefulWidget {
  final ThemeMode themeMode;
  final VoidCallback? onToggleTheme;
  const SettingsPage({
    Key? key,
    this.themeMode = ThemeMode.light,
    this.onToggleTheme,
  }) : super(key: key);

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  int durationType = 0;
  int dateType = 0;
  int startType = 0;
  bool showNano = true;
  String nanoIp = '';
  String nanoUser = '';
  String nanoPass = '';

  /// دالة لحساب تاريخ انتهاء الاشتراك بناءً على نوع المدة
  DateTime calculateSubscriptionEndDate(DateTime startDate, int durationType) {
    if (durationType == 0) {
      // حسب مدة الشهر: نفس اليوم من الشهر القادم أو آخر يوم في الشهر إذا لم يوجد
      int year = startDate.year;
      int month = startDate.month + 1;
      if (month > 12) {
        month = 1;
        year += 1;
      }
      int day = startDate.day;
      // احسب آخر يوم في الشهر الجديد
      int lastDayOfNextMonth = DateTime(year, month + 1, 0).day;
      if (day > lastDayOfNextMonth) day = lastDayOfNextMonth;
      return DateTime(
        year,
        month,
        day,
        startDate.hour,
        startDate.minute,
        startDate.second,
      );
    } else {
      // 30 يوم ثابتة
      return startDate.add(const Duration(days: 30));
    }
  }

  @override
  void initState() {
    super.initState();
    _loadDurationType();
    _loadShowNano();
  }

  Future<void> _loadDurationType() async {
    final type = await DBHelper.instance.getDurationType();
    setState(() {
      durationType = type;
    });
  }

  Future<void> _updateDurationType(int type) async {
    setState(() {
      durationType = type;
    });
    await DBHelper.instance.setDurationType(type);
  }

  Future<void> _loadShowNano() async {
    final db = await DBHelper.instance.database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: ['showNano'],
      limit: 1,
    );
    setState(() {
      showNano = result.isEmpty ? true : (result.first['value'] == '1');
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // محتوى الإعدادات
                  _buildSettingsContent(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.18),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.white.withValues(
              alpha: isDark ? 0.08 : 0.18,
            ),
            child: Icon(Icons.settings, color: colorScheme.primary, size: 54),
          ),
        ),
        // عنوان الشاشة
        Text(
          'الإعدادات',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(
                color: colorScheme.shadow.withValues(alpha: 0.13),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'إدارة إعدادات التطبيق والنظام',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withValues(alpha: 0.92),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء محتوى الإعدادات
  Widget _buildSettingsContent(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // قسم إعدادات النظام
        _buildModernSection(
          'إعدادات النظام',
          Icons.settings_applications,
          colorScheme,
          isDark,
          [
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('اعدادت النانو الافتراضية'),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('قريباً'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.source),
              title: const Text('المصادر'),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SourcesScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.list_alt),
              title: const Text('الباقات'),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionPricesScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('سجل العمليات'),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const TransactionsLogScreen(),
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.backup),
              title: const Text('النسخ الاحتياطي والاستعادة'),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const BackupRestoreScreen(),
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.lock_reset),
              title: const Text('تحديث كلمة المرور'),
              subtitle: const Text('تغيير كلمة مرور الحساب'),
              onTap: () async {
                // التحقق من صحة الجلسة أولاً
                final authService = SupabaseAuthService();
                final isSessionValid = await authService.isSessionValid();

                if (!isSessionValid) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                  return;
                }

                // الانتقال لشاشة تحديث كلمة المرور
                if (mounted) {
                  final result = await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const UpdatePasswordScreen(),
                    ),
                  );

                  // إذا تم التحديث بنجاح، أظهر رسالة
                  if (result == true && mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تحديث كلمة المرور بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                }
              },
            ),

            ListTile(
              leading: const Icon(Icons.help_outline),
              title: const Text('دليل التطبيق'),
              subtitle: const Text('إعادة عرض شاشة التعريف بالتطبيق'),
              onTap: () => _showIntroductionAgain(),
            ),

            ListTile(
              leading: const Icon(Icons.attach_money),
              title: const Text('الأرباح'),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ProfitsScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('تنبيهات واتساب'),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AutoNotificationsScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange, Colors.deepOrange],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.announcement,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              title: Row(
                children: [
                  const Text(
                    'إعلان مهم',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'جديد',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              subtitle: const Text(
                'ميزات جديدة قادمة قريباً',
                style: TextStyle(fontSize: 12),
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _showModernAnnouncementDialog(context),
            ),
            ListTile(
              leading: const Icon(Icons.opacity),
              title: const Text('Ping'),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const PingScreen()),
                );
              },
            ),
            // استيراد شاشة Ping
            ListTile(
              leading: const Icon(Icons.privacy_tip),
              title: const Text('سياسة الخصوصية'),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const PrivacyPolicyScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.message),
              title: const Text('تواصل معنا'),
              onTap: () async {
                final supportPhone = MainHomeScreen.supportPhone;
                final msg = 'مرحباً، أحتاج إلى دعم فني في تطبيق iTower.';
                final whatsappUrl = Uri.parse(
                  'https://wa.me/$supportPhone?text=${Uri.encodeComponent(msg)}',
                );
                try {
                  final launched = await launchUrl(
                    whatsappUrl,
                    mode: LaunchMode.externalApplication,
                  );
                  if (!launched) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'تعذر فتح واتساب. تأكد من وجود التطبيق وصحة الرقم.',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تعذر إرسال رسالة واتساب: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
            ),
          ],
        ),
        const SizedBox(height: 24),

        // قسم المظهر
        _buildThemeSection(colorScheme, isDark),
        const SizedBox(height: 24),

        // قسم قاعدة البيانات
        _buildDatabaseSection(colorScheme, isDark),
        const SizedBox(height: 24),

        // قسم معلومات التطبيق
        _buildModernSection(
          'معلومات التطبيق',
          Icons.info,
          colorScheme,
          isDark,
          [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surface.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.apps, color: colorScheme.primary, size: 24),
                      const SizedBox(width: 12),
                      Text(
                        'iTower v1.0.0',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'تطبيق إدارة شبكات الإنترنت',
                    style: TextStyle(
                      fontSize: 14,
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Divider(color: colorScheme.outline.withValues(alpha: 0.2)),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.person, color: colorScheme.primary, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'تم التطوير بواسطة: Salah-IT',
                        style: TextStyle(
                          fontSize: 12,
                          color: colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.copyright,
                        color: colorScheme.primary,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'جميع الحقوق محفوظة © 2025',
                        style: TextStyle(
                          fontSize: 12,
                          color: colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  // إعادة عرض شاشة الاستعراض
  Future<void> _showIntroductionAgain() async {
    try {
      // إعادة تعيين حالة الاستعراض
      await IntroductionService.resetIntroduction();

      if (mounted) {
        // عرض رسالة تأكيد
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('سيتم عرض دليل التطبيق عند إعادة تشغيل التطبيق'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // يمكن إضافة خيار لإعادة تشغيل التطبيق فوراً
        _showRestartDialog();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة تعيين الاستعراض: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // عرض حوار إعادة التشغيل
  void _showRestartDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تشغيل التطبيق'),
        content: const Text(
          'لعرض دليل التطبيق، يجب إعادة تشغيل التطبيق.\n'
          'هل تريد إغلاق التطبيق الآن؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لاحقاً'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // يمكن إضافة منطق إغلاق التطبيق هنا إذا أردت
              // SystemNavigator.pop(); // يتطلب import 'package:flutter/services.dart';
            },
            child: const Text('إغلاق التطبيق'),
          ),
        ],
      ),
    );
  }

  // بناء قسم عصري
  Widget _buildModernSection(
    String title,
    IconData icon,
    ColorScheme colorScheme,
    bool isDark,
    List<Widget> children,
  ) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: colorScheme.primary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  // بناء عنصر قائمة عصري
  Widget _buildModernListTile(
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
    ColorScheme colorScheme,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: colorScheme.primary, size: 20),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: colorScheme.outline,
        ),
        onTap: onTap,
      ),
    );
  }

  // بناء قسم المظهر
  Widget _buildThemeSection(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    widget.themeMode == ThemeMode.dark
                        ? Icons.dark_mode
                        : Icons.light_mode,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'المظهر',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: colorScheme.surface.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    widget.themeMode == ThemeMode.dark
                        ? Icons.dark_mode
                        : Icons.light_mode,
                    color: colorScheme.primary,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      widget.themeMode == ThemeMode.dark
                          ? 'الوضع الليلي'
                          : 'الوضع النهاري',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),
                  Switch(
                    value: widget.themeMode == ThemeMode.dark,
                    onChanged: (value) {
                      widget.onToggleTheme?.call();
                    },
                    activeColor: colorScheme.primary,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم قاعدة البيانات
  Widget _buildDatabaseSection(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.storage, color: Colors.red, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'قاعدة البيانات',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.red, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'منطقة خطر',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'حذف جميع البيانات سيؤدي إلى فقدان جميع المشتركين والمعاملات نهائياً',
                    style: TextStyle(color: Colors.red.shade700, fontSize: 14),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showDeleteAllDataDialog(context),
                      icon: const Icon(Icons.delete_forever, size: 20),
                      label: const Text('حذف جميع البيانات'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // عرض حوار معلومات التطبيق
  void _showAppInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Row(
          children: [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 12),
            Text('معلومات التطبيق'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'iTower v1.0.0',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('تطبيق إدارة شبكات الإنترنت'),
            SizedBox(height: 8),
            Text('تم التطوير بواسطة: Salah-IT'),
            SizedBox(height: 8),
            Text('جميع الحقوق محفوظة © 2025'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  // عرض حوار الإعلان العصري
  void _showModernAnnouncementDialog(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    showDialog(
      context: context,
      builder: (ctx) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              colors: [
                colorScheme.primary.withValues(alpha: 0.1),
                colorScheme.surface,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس الإعلان
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange, Colors.deepOrange],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.withValues(alpha: 0.3),
                      blurRadius: 16,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.announcement,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              // عنوان الإعلان
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'إعلان مهم',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'جديد',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // محتوى الإعلان
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.surface.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.rocket_launch,
                          color: Colors.orange,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'ميزات جديدة قادمة قريباً!',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'قريباً سوف نقوم بتوفير تحكم كامل في السيرفر من خلال التطبيق، مع إمكانيات متقدمة تشمل:',
                      style: TextStyle(
                        fontSize: 14,
                        color: colorScheme.onSurface.withValues(alpha: 0.8),
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // قائمة الميزات
                    _buildFeatureItem(
                      Icons.add_circle,
                      'إضافة سيرفر جديد',
                      colorScheme,
                    ),
                    _buildFeatureItem(
                      Icons.card_membership,
                      'دعم صفحات Earthlink',
                      colorScheme,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // أزرار الإجراء
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(ctx).pop(),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey[600],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('إغلاق'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(ctx).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Row(
                              children: [
                                Icon(Icons.notifications, color: Colors.white),
                                SizedBox(width: 12),
                                Text('سيتم إشعارك عند توفر التحديث'),
                              ],
                            ),
                            backgroundColor: Colors.green,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        );
                      },
                      icon: const Icon(Icons.notifications_active, size: 18),
                      label: const Text('تذكيرني'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء عنصر ميزة
  Widget _buildFeatureItem(
    IconData icon,
    String text,
    ColorScheme colorScheme,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, size: 14, color: Colors.orange),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 13,
                color: colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // عرض حوار حذف جميع البيانات
  Future<void> _showDeleteAllDataDialog(BuildContext context) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.warning, color: Colors.red, size: 24),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'تأكيد الحذف',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: const Text(
          'هل أنت متأكد أنك تريد حذف قاعدة البيانات بالكامل؟\n\nسيتم حذف جميع المشتركين والمعاملات ولا يمكن التراجع عن هذه العملية.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(false),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey[600],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(ctx).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('حذف نهائياً'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      final dbPath = await sqflite.getDatabasesPath();
      final path = '$dbPath/itower.db';
      await sqflite.deleteDatabase(path);
      // إعادة تهيئة الاتصال بقاعدة البيانات بعد الحذف
      await DBHelper.instance.reopenDatabase();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 12),
                Text('تم حذف قاعدة البيانات بنجاح'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
        // إعادة التطبيق إلى شاشة البداية (Splash/Login)
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    }
  }
}
