import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'simple_root_screen.dart';
import 'services/supabase_auth_service.dart';
import 'services/account_service.dart';
import 'forgot_password_screen.dart';
import 'device_limit_dialog.dart';

class SupabaseLoginScreen extends StatefulWidget {
  const SupabaseLoginScreen({super.key});

  @override
  State<SupabaseLoginScreen> createState() => _SupabaseLoginScreenState();
}

class _SupabaseLoginScreenState extends State<SupabaseLoginScreen> {
  final _authService = SupabaseAuthService();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _isSignUp = false;
  bool _obscurePassword = true;
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _displayNameController.dispose();
    super.dispose();
  }

  // انتظار إنشاء الجلسة
  Future<bool> _waitForSession({int maxWaitSeconds = 10}) async {
    debugPrint('🔍 [SESSION] انتظار إنشاء الجلسة...');

    for (int i = 0; i < maxWaitSeconds * 2; i++) {
      final session = Supabase.instance.client.auth.currentSession;
      if (session != null) {
        debugPrint('✅ [SESSION] تم إنشاء الجلسة بنجاح بعد ${i * 0.5} ثانية');
        debugPrint(
          '✅ [SESSION] Access Token: ${session.accessToken.substring(0, 20)}...',
        );
        debugPrint('✅ [SESSION] انتهاء الجلسة: ${session.expiresAt}');
        return true;
      }

      debugPrint('⏳ [SESSION] انتظار... محاولة ${i + 1}');
      await Future.delayed(const Duration(milliseconds: 500));
    }

    debugPrint('❌ [SESSION] فشل في إنشاء الجلسة خلال $maxWaitSeconds ثانية');
    return false;
  }

  // الحصول على معرف الجهاز الثابت
  Future<String> getDeviceId() async {
    try {
      // أولاً، محاولة الحصول على المعرف المحفوظ محلياً
      final prefs = await SharedPreferences.getInstance();
      String? savedDeviceId = prefs.getString('permanent_device_id');

      if (savedDeviceId != null && savedDeviceId.isNotEmpty) {
        debugPrint('🔍 [DEVICE_ID] استخدام المعرف المحفوظ: $savedDeviceId');
        return savedDeviceId;
      }

      // إذا لم يوجد معرف محفوظ، إنشاء معرف جديد
      final deviceInfo = DeviceInfoPlugin();
      String deviceId = '';

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;

        // استخدام معرف ثابت للأندرويد
        // الأولوية: fingerprint > id > model+brand
        if (androidInfo.fingerprint.isNotEmpty) {
          deviceId = androidInfo.fingerprint;
          debugPrint('🔍 [DEVICE_ID] إنشاء معرف من Fingerprint: $deviceId');
        } else if (androidInfo.id.isNotEmpty) {
          deviceId = androidInfo.id;
          debugPrint('🔍 [DEVICE_ID] إنشاء معرف من Build ID: $deviceId');
        } else {
          // استخدام مزيج من model و brand كبديل
          deviceId =
              '${androidInfo.brand}_${androidInfo.model}_${androidInfo.device}';
          debugPrint('🔍 [DEVICE_ID] إنشاء معرف من Model+Brand: $deviceId');
        }

        deviceId = 'android_$deviceId';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        final iosDeviceId = iosInfo.identifierForVendor ?? 'unknown_ios';
        deviceId = 'ios_$iosDeviceId';
        debugPrint('🔍 [DEVICE_ID] إنشاء معرف iOS: $deviceId');
      } else {
        deviceId = 'unknown_platform_${DateTime.now().millisecondsSinceEpoch}';
      }

      // حفظ المعرف الجديد محلياً
      await prefs.setString('permanent_device_id', deviceId);
      debugPrint('✅ [DEVICE_ID] تم حفظ المعرف الجديد: $deviceId');

      return deviceId;
    } catch (e) {
      debugPrint('❌ [DEVICE_ID] خطأ في الحصول على معرف الجهاز: $e');
      // في حالة الخطأ، إنشاء معرف مؤقت
      final fallbackId =
          'error_device_id_${DateTime.now().millisecondsSinceEpoch}';

      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('permanent_device_id', fallbackId);
      } catch (e2) {
        debugPrint('❌ [DEVICE_ID] خطأ في حفظ المعرف الاحتياطي: $e2');
      }

      return fallbackId;
    }
  }

  // مسح معرف الجهاز المحفوظ (للاختبار فقط)
  Future<void> clearDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('permanent_device_id');
      debugPrint('🗑️ [DEVICE_ID] تم مسح معرف الجهاز المحفوظ');
    } catch (e) {
      debugPrint('❌ [DEVICE_ID] خطأ في مسح معرف الجهاز: $e');
    }
  }

  Future<void> _handleAuth() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;
      final displayName = _displayNameController.text.trim();

      // التحقق من معرف الجهاز
      final deviceId = await getDeviceId();
      if (deviceId.isEmpty) {
        throw Exception('لا يمكن الحصول على معرف الجهاز');
      }

      AuthResponse response;
      if (_isSignUp) {
        // التحقق من أن اسم المستخدم مطلوب للتسجيل
        if (displayName.isEmpty) {
          throw Exception('اسم المستخدم مطلوب للتسجيل');
        }

        // فحص الجهاز قبل إنشاء الحساب
        debugPrint('🔍 [PRE_SIGNUP] فحص الجهاز قبل إنشاء الحساب...');
        debugPrint('🔍 [PRE_SIGNUP] معرف الجهاز: $deviceId');

        final existingUserId = await AccountService.getExistingAccountForDevice(
          deviceId,
        );
        if (existingUserId != null) {
          debugPrint('⚠️ [PRE_SIGNUP] وجد حساب موجود للجهاز: $existingUserId');

          // إظهار حوار التحذير
          if (mounted) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) =>
                  DeviceLimitDialog(existingUserId: existingUserId),
            );
          }
          return; // إيقاف العملية نهائياً قبل إنشاء الحساب
        }

        debugPrint('✅ [PRE_SIGNUP] الجهاز جديد - يمكن إنشاء الحساب');

        response = await _authService.signUpWithEmail(
          email: email,
          password: password,
        );

        // إنشاء الحساب فعلياً مع تتبع مفصل
        if (response.user != null) {
          try {
            debugPrint(
              '🔍 [LOGIN] بدء إعداد الحساب للمستخدم: ${response.user!.id}',
            );
            debugPrint('🔍 [LOGIN] البريد الإلكتروني: ${response.user!.email}');
            debugPrint('🔍 [LOGIN] اسم المستخدم المطلوب: $displayName');
            debugPrint(
              '🔍 [LOGIN] حالة تأكيد البريد: ${response.user!.emailConfirmedAt}',
            );
            debugPrint(
              '🔍 [LOGIN] آخر تسجيل دخول: ${response.user!.lastSignInAt}',
            );
            debugPrint('🔍 [LOGIN] تاريخ الإنشاء: ${response.user!.createdAt}');

            // تم فحص الجهاز مسبقاً قبل إنشاء الحساب
            debugPrint('🔍 [LOGIN] معرف الجهاز: $deviceId');

            // فحص الجلسة قبل البدء
            final session = Supabase.instance.client.auth.currentSession;
            debugPrint(
              '🔍 [LOGIN] حالة الجلسة: ${session != null ? "موجودة" : "غير موجودة"}',
            );
            if (session != null) {
              debugPrint(
                '🔍 [LOGIN] Access Token: ${session.accessToken.substring(0, 20)}...',
              );
              debugPrint('🔍 [LOGIN] انتهاء الجلسة: ${session.expiresAt}');
            }

            // فحص إذا كان البريد الإلكتروني يحتاج تأكيد
            if (response.user!.emailConfirmedAt == null) {
              debugPrint(
                '⚠️ [LOGIN] البريد الإلكتروني غير مؤكد - محاولة تأكيد تلقائي',
              );

              // محاولة تأكيد البريد تلقائياً (للتطوير)
              try {
                debugPrint('🔧 [LOGIN] محاولة تأكيد البريد تلقائياً...');

                // تسجيل دخول مباشر بدون تأكيد (للتطوير فقط)
                final signInResponse = await _authService.signInWithEmail(
                  email: email,
                  password: password,
                );

                if (signInResponse.user != null) {
                  debugPrint(
                    '✅ [LOGIN] تم تسجيل الدخول بنجاح رغم عدم تأكيد البريد',
                  );
                  response = signInResponse;
                } else {
                  throw Exception('فشل في تسجيل الدخول التلقائي');
                }
              } catch (e) {
                debugPrint('❌ [LOGIN] فشل في التأكيد التلقائي: $e');

                // حفظ البيانات محلياً والمتابعة بدون جلسة Supabase
                await _saveUserData(response.user!, displayName: displayName);

                // إنشاء حساب محلي فقط
                debugPrint('🔧 [LOGIN] إنشاء حساب محلي بدون Supabase...');
                await _navigateToHome();
                return;
              }
            }

            // انتظار إنشاء الجلسة الفعلي
            debugPrint('🔍 [LOGIN] انتظار إنشاء الجلسة...');
            final sessionCreated = await _waitForSession(maxWaitSeconds: 10);

            if (!sessionCreated) {
              throw Exception('فشل في إنشاء الجلسة خلال 10 ثوان');
            }

            // فحص الجلسة مرة أخيرة قبل تحديث display name
            final finalSession = Supabase.instance.client.auth.currentSession;
            debugPrint(
              '🔍 [LOGIN] فحص الجلسة النهائي: ${finalSession != null ? "موجودة" : "غير موجودة"}',
            );

            if (finalSession == null) {
              throw Exception('الجلسة غير متاحة رغم الانتظار');
            }

            // تحديث display name في Auth
            debugPrint('🔍 [LOGIN] محاولة تحديث display name...');
            await Supabase.instance.client.auth.updateUser(
              UserAttributes(data: {'display_name': displayName}),
            );
            debugPrint('✅ [LOGIN] تم تحديث display name: $displayName');

            // انتظار إضافي قبل إنشاء الحساب
            debugPrint('🔍 [LOGIN] انتظار إضافي قبل إنشاء الحساب...');
            await Future.delayed(const Duration(milliseconds: 500));

            // إنشاء سجل في جدول user_accounts
            debugPrint('🔍 [LOGIN] محاولة إنشاء سجل الحساب...');
            await AccountService.createAccount(
              response.user!.id,
              displayName: displayName,
            );
            debugPrint('✅ [LOGIN] تم إنشاء سجل في user_accounts بنجاح');
          } catch (e) {
            debugPrint('❌ [LOGIN] خطأ في إعداد الحساب: $e');
            debugPrint('❌ [LOGIN] نوع الخطأ: ${e.runtimeType}');

            // فحص الجلسة عند الخطأ
            final sessionAfterError =
                Supabase.instance.client.auth.currentSession;
            debugPrint(
              '❌ [LOGIN] حالة الجلسة بعد الخطأ: ${sessionAfterError != null ? "موجودة" : "غير موجودة"}',
            );

            // معالجة خاصة لخطأ الجهاز المرتبط
            if (e.toString().contains('يوجد حساب آخر مرتبط بهذا الجهاز')) {
              // إظهار حوار التحذير
              if (mounted) {
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) => DeviceLimitDialog(
                    existingUserId: '', // يمكن تمرير معرف المستخدم الموجود
                  ),
                );
              }
              return; // لا نرمي استثناء، فقط نظهر الحوار
            }

            // إيقاف العملية - إنشاء الحساب مطلوب
            throw Exception('فشل في إعداد الحساب: ${e.toString()}');
          }
        }
      } else {
        response = await _authService.signInWithEmail(
          email: email,
          password: password,
        );

        // للمستخدمين الحاليين، تحقق من وجود سجل في user_accounts
        if (response.user != null) {
          try {
            // انتظار إنشاء الجلسة للمستخدمين الحاليين
            debugPrint('🔍 [LOGIN] انتظار الجلسة للمستخدم الحالي...');
            final sessionCreated = await _waitForSession(maxWaitSeconds: 5);

            if (!sessionCreated) {
              throw Exception('فشل في إنشاء الجلسة للمستخدم الحالي');
            }

            final existingAccount = await AccountService.getAccountData(
              response.user!.id,
              useCache: false,
            );
            if (existingAccount == null) {
              // إنشاء سجل للمستخدم الحالي فعلياً
              final userDisplayName =
                  response.user!.userMetadata?['display_name'] ??
                  response.user!.email?.split('@')[0] ??
                  'مستخدم';

              await AccountService.createAccount(
                response.user!.id,
                displayName: userDisplayName,
              );
              debugPrint('تم إنشاء سجل user_accounts للمستخدم الحالي');
            } else {
              // التحقق من حالة الحساب
              final isTrialExpired = await AccountService.isTrialExpired(
                response.user!.id,
              );
              if (isTrialExpired) {
                throw Exception(
                  'انتهت الفترة التجريبية. يرجى تفعيل الحساب للمتابعة.',
                );
              }
            }
          } catch (e) {
            debugPrint('خطأ في التحقق من سجل المستخدم: $e');
            // إيقاف العملية إذا كان خطأ مهم
            if (e.toString().contains('انتهت الفترة التجريبية')) {
              rethrow;
            }
            // للأخطاء الأخرى، نرمي استثناء واضح
            throw Exception('فشل في إعداد بيانات المستخدم: ${e.toString()}');
          }
        }
      }

      if (response.user != null) {
        // ربط الجهاز بالحساب
        try {
          debugPrint('🔍 [LOGIN] محاولة ربط الجهاز بالحساب...');
          await AccountService.linkDevice(
            response.user!.id,
            deviceId,
            deviceName: 'جهاز ${Platform.isAndroid ? 'Android' : 'iOS'}',
          );
          debugPrint('✅ [LOGIN] تم ربط الجهاز بالحساب بنجاح');
        } catch (e) {
          debugPrint('❌ [LOGIN] خطأ في ربط الجهاز: $e');

          // إذا كان الخطأ بسبب وجود حساب آخر، نوقف العملية
          if (e.toString().contains('يوجد حساب آخر مرتبط بهذا الجهاز')) {
            rethrow;
          }

          // إذا كان خطأ في قاعدة البيانات، نوقف العملية أيضاً
          if (e.toString().contains('Could not find') ||
              e.toString().contains('PGRST204') ||
              e.toString().contains('schema cache')) {
            debugPrint('🚨 [LOGIN] خطأ خطير في قاعدة البيانات - إيقاف العملية');
            throw Exception(
              'خطأ في إعداد قاعدة البيانات. يرجى التواصل مع الدعم الفني.',
            );
          }

          // للأخطاء الأخرى فقط، نستمر بالعملية
          debugPrint('⚠️ [LOGIN] تم تجاهل خطأ ربط الجهاز والمتابعة');
        }

        await _saveUserData(response.user!, displayName: displayName);
        await _navigateToHome();
      } else {
        throw Exception('فشل في المصادقة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في المصادقة: $e');

      // لا نظهر رسالة خطأ إذا تم إظهار حوار التحذير
      if (e.toString().contains('يوجد حساب آخر مرتبط بهذا الجهاز')) {
        debugPrint('🚨 [AUTH] تم إظهار حوار التحذير - لا حاجة لرسالة خطأ');
      } else {
        setState(() {
          _errorMessage = _getErrorMessage(e.toString());
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleAnonymousLogin() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _authService.signInAnonymously();
      if (response.user != null) {
        await _saveUserData(response.user!);
        await _navigateToHome();
      }
    } catch (e) {
      setState(() {
        _errorMessage = _getErrorMessage(e.toString());
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // فحص ربط الجهاز بالحساب (مبسط)
  Future<void> _checkDeviceBinding(User user, String deviceId) async {
    try {
      debugPrint('محاولة ربط الجهاز للمستخدم: ${user.id}');

      // محاولة إدراج سجل جديد (سيفشل إذا كان موجود)
      await Supabase.instance.client.from('user_devices').insert({
        'user_id': user.id,
        'device_id': deviceId,
        'created_at': DateTime.now().toIso8601String(),
      });

      debugPrint('تم ربط الجهاز بنجاح');
    } catch (e) {
      debugPrint('خطأ في ربط الجهاز: $e');

      // إذا كان الخطأ بسبب وجود السجل مسبقاً، نتحقق من التطابق
      if (e.toString().contains('duplicate') ||
          e.toString().contains('unique')) {
        try {
          final existing = await Supabase.instance.client
              .from('user_devices')
              .select('device_id')
              .eq('user_id', user.id)
              .single();

          if (existing['device_id'] != deviceId) {
            throw Exception('هذا الحساب مرتبط بجهاز آخر');
          }

          debugPrint('الجهاز مرتبط مسبقاً بنفس الحساب');
        } catch (checkError) {
          debugPrint('خطأ في التحقق من الربط: $checkError');
          // نتجاهل الخطأ ونستمر
        }
      }
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('Invalid login credentials')) {
      return 'بيانات تسجيل الدخول غير صحيحة';
    } else if (error.contains('User already registered')) {
      return 'هذا البريد مسجل مسبقاً، جرب تسجيل الدخول';
    } else if (error.contains('Invalid email')) {
      return 'البريد الإلكتروني غير صحيح';
    } else if (error.contains('Password should be at least 6 characters')) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else if (error.contains('هذا الحساب مرتبط') ||
        error.contains('يوجد حساب آخر')) {
      return error;
    }
    return 'حدث خطأ: $error';
  }

  Future<void> _saveUserData(User user, {String? displayName}) async {
    final prefs = await SharedPreferences.getInstance();

    // حفظ بيانات المستخدم
    await prefs.setString('user_id', user.id);
    await prefs.setString('user_email', user.email ?? '');

    // حفظ اسم المستخدم من البيانات أو المعامل
    final userName =
        displayName ??
        user.userMetadata?['display_name'] ??
        user.email?.split('@')[0] ??
        'مستخدم';
    await prefs.setString('user_display_name', userName);

    await prefs.setBool('is_logged_in', true);

    // إعداد بيانات التجربة (30 يوم)
    final now = DateTime.now();
    final expiryDate = now.add(const Duration(days: 30));
    await prefs.setInt('expiry_millis', expiryDate.millisecondsSinceEpoch);
    await prefs.setBool('is_trial', true);

    // حفظ معلومات الجهاز
    await _saveDeviceInfo(user.id);
  }

  Future<void> _saveDeviceInfo(String userId) async {
    final deviceId = await getDeviceId();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('device_id', deviceId);
  }

  Future<void> _navigateToHome() async {
    if (!mounted) return;
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (_) => SimpleRootScreen(
          onLoginSuccess: () {
            // سيتم تنفيذ المزامنة بعد تسجيل الدخول
          },
        ),
      ),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.primary,
              colorScheme.primary.withValues(alpha: 0.8),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 40),
                // شعار التطبيق
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.cell_tower_rounded,
                    size: 60,
                    color: colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'iTower',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'لوحة تحكم احترافية لإدارة المشتركين والديون',
                  style: TextStyle(
                    fontSize: 17,
                    color: colorScheme.onPrimary.withValues(alpha: 0.92),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                // نموذج تسجيل الدخول
                Card(
                  elevation: 0,
                  color: colorScheme.surface.withValues(
                    alpha: isDark ? 0.7 : 0.93,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(22),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // تبديل بين تسجيل الدخول والتسجيل
                          Row(
                            children: [
                              Expanded(
                                child: TextButton(
                                  onPressed: () =>
                                      setState(() => _isSignUp = false),
                                  style: TextButton.styleFrom(
                                    backgroundColor: !_isSignUp
                                        ? colorScheme.primary
                                        : Colors.transparent,
                                    foregroundColor: !_isSignUp
                                        ? colorScheme.onPrimary
                                        : colorScheme.onSurface,
                                  ),
                                  child: const Text('تسجيل الدخول'),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextButton(
                                  onPressed: () =>
                                      setState(() => _isSignUp = true),
                                  style: TextButton.styleFrom(
                                    backgroundColor: _isSignUp
                                        ? colorScheme.primary
                                        : Colors.transparent,
                                    foregroundColor: _isSignUp
                                        ? colorScheme.onPrimary
                                        : colorScheme.onSurface,
                                  ),
                                  child: const Text('إنشاء حساب'),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),
                          // حقل البريد الإلكتروني
                          TextFormField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: const InputDecoration(
                              labelText: 'البريد الإلكتروني',
                              prefixIcon: Icon(Icons.email),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال البريد الإلكتروني';
                              }
                              if (!value.contains('@')) {
                                return 'البريد الإلكتروني غير صحيح';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // حقل اسم المستخدم (للتسجيل فقط)
                          if (_isSignUp) ...[
                            TextFormField(
                              controller: _displayNameController,
                              decoration: const InputDecoration(
                                labelText: 'اسم المستخدم',
                                prefixIcon: Icon(Icons.person),
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (_isSignUp &&
                                    (value == null || value.trim().isEmpty)) {
                                  return 'اسم المستخدم مطلوب';
                                }
                                if (value != null && value.trim().length < 2) {
                                  return 'اسم المستخدم يجب أن يكون حرفين على الأقل';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                          ],

                          // حقل كلمة المرور
                          TextFormField(
                            controller: _passwordController,
                            obscureText: _obscurePassword,
                            decoration: InputDecoration(
                              labelText: 'كلمة المرور',
                              prefixIcon: const Icon(Icons.lock),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility
                                      : Icons.visibility_off,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال كلمة المرور';
                              }
                              if (value.length < 6) {
                                return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 24),
                          // رسالة الخطأ
                          if (_errorMessage != null)
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: colorScheme.error.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: colorScheme.error.withValues(
                                    alpha: 0.3,
                                  ),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    color: colorScheme.error,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      _errorMessage!,
                                      style: TextStyle(
                                        color: colorScheme.error,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (_errorMessage != null) const SizedBox(height: 16),
                          // أزرار العمل
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _handleAuth,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: colorScheme.primary,
                                foregroundColor: colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: _isLoading
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                      ),
                                    )
                                  : Text(
                                      _isSignUp ? 'إنشاء حساب' : 'تسجيل الدخول',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),

                          // زر نسيت كلمة المرور (فقط في وضع تسجيل الدخول)
                          if (!_isSignUp) ...[
                            const SizedBox(height: 16),
                            TextButton(
                              onPressed: _isLoading
                                  ? null
                                  : () {
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              const ForgotPasswordScreen(),
                                        ),
                                      );
                                    },
                              child: Text(
                                'نسيت كلمة المرور؟',
                                style: TextStyle(
                                  color: colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                // معلومات إضافية
                Text(
                  'بتسجيل الدخول، أنت توافق على شروط الاستخدام',
                  style: TextStyle(
                    fontSize: 12,
                    color: colorScheme.onPrimary.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
