import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../Backup_Restore_Screen.dart';
import '../settings_page.dart';
import '../services/account_service.dart';
import 'activation_options_screen.dart';
import 'activation_by_code_screen.dart';

class AccountScreen extends StatefulWidget {
  final File? profileImageFile;
  final Function(File?) onImageChanged;

  const AccountScreen({
    super.key,
    this.profileImageFile,
    required this.onImageChanged,
  });

  @override
  State<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends State<AccountScreen>
    with TickerProviderStateMixin {
  String userName = '';
  bool isTrial = true;
  String packageText = '';
  String expiryText = '';
  int daysLeft = 0;
  bool isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // متغيرات التفعيل الإضافية
  String activePackage = '';
  DateTime? activationDate;
  DateTime? expiryDate;
  bool needsActivation = false;
  bool isNearExpiry = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );
    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
          ),
        );
    _loadAccountData();
    _animationController.forward();
  }

  Future<void> _loadAccountData() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return;

      userName = user.userMetadata?['display_name'] ?? user.email ?? 'مستخدم';

      // جلب بيانات المستخدم من Supabase
      final accountData = await AccountService.getAccountData(user.id);

      if (accountData != null) {
        isTrial = accountData['is_trial'] ?? true;

        if (isTrial) {
          packageText = 'حساب تجريبي';
          needsActivation = true; // دائماً يحتاج تفعيل للحسابات التجريبية

          // حساب الأيام المتبقية للحساب التجريبي
          if (accountData['expiry_millis'] != null &&
              accountData['expiry_millis'] > 0) {
            expiryDate = DateTime.fromMillisecondsSinceEpoch(
              accountData['expiry_millis'],
            );
            daysLeft = expiryDate!.difference(DateTime.now()).inDays;
            if (daysLeft < 0) daysLeft = 0;
            expiryText = daysLeft > 0
                ? 'متبقي $daysLeft يوم'
                : 'انتهت الفترة التجريبية';
          } else {
            // إذا لم تكن هناك بيانات انتهاء، استخدم القيم الافتراضية
            daysLeft = 15;
            expiryText = 'متبقي $daysLeft يوم';
          }
        } else {
          packageText = 'حساب مفعل';
          needsActivation = false;

          // جلب معلومات الباقة المفعلة
          activePackage = accountData['active_package'] ?? 'غير محدد';

          if (accountData['expiry_millis'] != null &&
              accountData['expiry_millis'] > 0) {
            expiryDate = DateTime.fromMillisecondsSinceEpoch(
              accountData['expiry_millis'],
            );
            daysLeft = expiryDate!.difference(DateTime.now()).inDays;
            if (daysLeft < 0) daysLeft = 0;
            expiryText = daysLeft > 0
                ? 'متبقي $daysLeft يوم'
                : 'انتهت صلاحية الحساب';
            isNearExpiry = daysLeft <= 30 && daysLeft > 0; // قريب من الانتهاء
            needsActivation = daysLeft <= 0; // يحتاج تجديد إذا انتهى
          }

          // جلب تاريخ التفعيل
          if (accountData['activation_millis'] != null &&
              accountData['activation_millis'] > 0) {
            activationDate = DateTime.fromMillisecondsSinceEpoch(
              accountData['activation_millis'],
            );
          }

          // تحديث نص الباقة ليشمل اسم الباقة
          if (activePackage.isNotEmpty && activePackage != 'غير محدد') {
            packageText = 'باقة $activePackage';
          }
        }
      } else {
        // فحص الجهاز قبل إنشاء حساب جديد
        debugPrint(
          '⚠️ [ACCOUNT_SCREEN] محاولة إنشاء حساب جديد - فحص الجهاز أولاً',
        );

        // لا ننشئ حساب جديد هنا للأمان
        // المستخدم يجب أن يسجل دخول من شاشة التسجيل
        debugPrint(
          '❌ [ACCOUNT_SCREEN] منع إنشاء حساب جديد - يجب التسجيل من الشاشة الرئيسية',
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يرجى إعادة تسجيل الدخول من الشاشة الرئيسية'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الحساب: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        final directory = await getApplicationDocumentsDirectory();
        final fileName = 'profile_${DateTime.now().millisecondsSinceEpoch}.png';
        final savedImage = await File(
          pickedFile.path,
        ).copy('${directory.path}/$fileName');

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('profileImagePath', savedImage.path);

        widget.onImageChanged(savedImage);
      }
    } catch (e) {
      debugPrint('خطأ في اختيار الصورة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    if (isLoading) {
      return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: colorScheme.surface,
        appBar: AppBar(
          title: const Text(
            'حسابي',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          centerTitle: true,
          foregroundColor: Colors.white,
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark
                  ? [
                      colorScheme.primary.withValues(alpha: 0.9),
                      colorScheme.surface.withValues(alpha: 0.85),
                    ]
                  : [colorScheme.primary, colorScheme.surface],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
        ),
      );
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false, // إلغاء سهم الرجوع
        toolbarHeight: 70, // زيادة ارتفاع الـ toolbar لتنزيل كلمة "حسابي"
      ),
      body: Stack(
        children: [
          // خلفية متدرجة عصرية مثل شاشة تسجيل الدخول
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 0,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const SizedBox(
                            height: 0,
                          ), // مساحة أكبر للتنزيل وإبعاد المحتوى عن الأعلى
                          // شعار دائري عصري مع صورة المستخدم
                          Container(
                            margin: const EdgeInsets.only(bottom: 18),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: colorScheme.primary.withValues(
                                    alpha: 0.18,
                                  ),
                                  blurRadius: 24,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                widget.profileImageFile != null
                                    ? CircleAvatar(
                                        backgroundImage: FileImage(
                                          widget.profileImageFile!,
                                        ),
                                        radius: 64,
                                        backgroundColor: Colors.white
                                            .withValues(
                                              alpha: isDark ? 0.08 : 0.18,
                                            ),
                                      )
                                    : CircleAvatar(
                                        radius: 64,
                                        backgroundColor: Colors.white
                                            .withValues(
                                              alpha: isDark ? 0.08 : 0.18,
                                            ),
                                        child: Icon(
                                          Icons.person_rounded,
                                          color: colorScheme.primary,
                                          size: 64,
                                        ),
                                      ),
                                // زر التعديل
                                Positioned(
                                  bottom: 4,
                                  right: 4,
                                  child: GestureDetector(
                                    onTap: _pickImage,
                                    child: Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: colorScheme.primary,
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.2,
                                            ),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: const Icon(
                                        Icons.camera_alt_rounded,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // اسم المستخدم
                          Text(
                            userName.isNotEmpty ? userName : 'مستخدم',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onPrimary,
                              letterSpacing: 1,
                              shadows: [
                                Shadow(
                                  color: colorScheme.shadow.withValues(
                                    alpha: 0.13,
                                  ),
                                  blurRadius: 4,
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),

                          // وصف الحساب
                          Text(
                            isTrial ? 'حسابك تجريبي' : 'حساب مفعل',
                            style: TextStyle(
                              fontSize: 16,
                              color: colorScheme.onPrimary.withValues(
                                alpha: 0.92,
                              ),
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),

                          // بطاقة شفافة عصرية للمعلومات
                          Card(
                            elevation: 0,
                            color: colorScheme.surface.withValues(
                              alpha: isDark ? 0.7 : 0.93,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(22),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 32,
                                horizontal: 24,
                              ),
                              child: Column(
                                children: [
                                  // معلومات الحساب
                                  _buildAccountInfoSection(colorScheme, isDark),
                                  const SizedBox(height: 24),

                                  // قسم التفعيل (دائماً للتجريبي، عند الحاجة للمفعل)
                                  if (isTrial ||
                                      needsActivation ||
                                      isNearExpiry) ...[
                                    _buildActivationSection(
                                      colorScheme,
                                      isDark,
                                    ),
                                    const SizedBox(height: 24),
                                  ],

                                  // أزرار الإجراءات
                                  _buildActionButtons(colorScheme, isDark),
                                ],
                              ),
                            ),
                          ),

                          // مساحة إضافية في النهاية للتمرير المريح
                          const SizedBox(height: 80),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء قسم التفعيل
  Widget _buildActivationSection(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: needsActivation
              ? [
                  Colors.orange.withValues(alpha: 0.1),
                  Colors.red.withValues(alpha: 0.1),
                ]
              : [
                  Colors.amber.withValues(alpha: 0.1),
                  Colors.orange.withValues(alpha: 0.1),
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: needsActivation
              ? Colors.red.withValues(alpha: 0.3)
              : Colors.orange.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: (needsActivation ? Colors.red : Colors.orange).withValues(
              alpha: 0.1,
            ),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // أيقونة ونص التحذير
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getActivationColor().withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getActivationIcon(),
                  color: _getActivationColor(),
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getActivationTitle(),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: _getActivationColor(),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getActivationSubtitle(),
                      style: TextStyle(
                        fontSize: 14,
                        color: _getActivationColor().withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // أزرار التفعيل
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 3,
                  ),
                  icon: const Icon(Icons.shopping_cart_rounded, size: 20),
                  label: Text(
                    needsActivation
                        ? (isTrial ? 'تفعيل الحساب' : 'تجديد الاشتراك')
                        : 'تجديد مبكر',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => ActivationOptionsScreen(
                          onSelect: (type, amount) {
                            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  style: OutlinedButton.styleFrom(
                    foregroundColor: colorScheme.primary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    side: BorderSide(color: colorScheme.primary, width: 2),
                  ),
                  icon: const Icon(Icons.vpn_key_rounded, size: 20),
                  label: const Text(
                    'كود تفعيل',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ActivationByCodeScreen(),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دوال مساعدة لقسم التفعيل
  String _getActivationTitle() {
    if (isTrial) {
      return daysLeft <= 0
          ? 'انتهت الفترة التجريبية!'
          : 'حساب تجريبي - فعل الآن!';
    } else if (needsActivation) {
      return 'انتهت صلاحية الحساب!';
    } else {
      return 'قريب من الانتهاء!';
    }
  }

  String _getActivationSubtitle() {
    if (isTrial) {
      return daysLeft <= 0
          ? 'يجب تفعيل الحساب للمتابعة'
          : 'استمتع بجميع الميزات مع التفعيل';
    } else if (needsActivation) {
      return 'يجب تجديد الاشتراك للمتابعة';
    } else {
      return 'فكر في تجديد اشتراكك قريباً';
    }
  }

  Color _getActivationColor() {
    if (isTrial) {
      return daysLeft <= 0 ? Colors.red : Colors.blue;
    } else if (needsActivation) {
      return Colors.red;
    } else {
      return Colors.orange;
    }
  }

  IconData _getActivationIcon() {
    if (isTrial) {
      return daysLeft <= 0 ? Icons.warning_rounded : Icons.star_rounded;
    } else if (needsActivation) {
      return Icons.warning_rounded;
    } else {
      return Icons.schedule_rounded;
    }
  }

  // بناء قسم معلومات الحساب
  Widget _buildAccountInfoSection(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark
            ? colorScheme.surface.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_circle_rounded,
                color: colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'معلومات الحساب',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // البريد الإلكتروني
          _buildInfoRow(
            Icons.email_rounded,
            'البريد الإلكتروني',
            userName,
            Colors.blue,
          ),
          const SizedBox(height: 16),

          // نوع الحساب
          _buildInfoRow(
            isTrial ? Icons.schedule_rounded : Icons.verified_rounded,
            'نوع الحساب',
            packageText,
            isTrial ? Colors.orange : Colors.green,
          ),

          if (expiryText.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.calendar_today_rounded,
              'حالة الاشتراك',
              expiryText,
              daysLeft > 7 ? Colors.green : Colors.red,
            ),
          ],

          // معلومات إضافية للحسابات المفعلة
          if (!isTrial &&
              activePackage.isNotEmpty &&
              activePackage != 'غير محدد') ...[
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.card_membership_rounded,
              'الباقة النشطة',
              activePackage,
              Colors.blue,
            ),
          ],

          if (!isTrial && activationDate != null) ...[
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.event_available_rounded,
              'تاريخ التفعيل',
              '${activationDate!.day}/${activationDate!.month}/${activationDate!.year}',
              Colors.green,
            ),
          ],

          if (daysLeft > 0 && daysLeft <= 7) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning_rounded, color: Colors.orange, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'ينتهي اشتراكك قريباً! متبقي $daysLeft أيام فقط',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow(IconData icon, String label, String value, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // بناء أزرار الإجراءات
  Widget _buildActionButtons(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // زر النسخ الاحتياطي
        _buildActionButton(
          icon: Icons.backup_rounded,
          label: 'النسخ الاحتياطي',
          color: Colors.blue,
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const BackupRestoreScreen(),
              ),
            );
          },
        ),
        const SizedBox(height: 12),

        // زر الإعدادات
        _buildActionButton(
          icon: Icons.settings_rounded,
          label: 'الإعدادات',
          color: Colors.purple,
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => SettingsPage(
                  themeMode: Theme.of(context).brightness == Brightness.dark
                      ? ThemeMode.dark
                      : ThemeMode.light,
                  onToggleTheme: () {
                    // يمكن تمرير دالة فارغة أو null حسب التصميم
                  },
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 12),

        // زر حول التطبيق
        _buildActionButton(
          icon: Icons.info_rounded,
          label: 'حول التطبيق',
          color: Colors.teal,
          onTap: () {
            showAboutDialog(
              context: context,
              applicationName: 'iTower',
              applicationVersion: '1.0.0',
              applicationIcon: const Icon(Icons.router),
              children: [const Text('تطبيق إدارة المشتركين والديون')],
            );
          },
        ),
        const SizedBox(height: 20),

        // زر تسجيل الخروج
        _buildActionButton(
          icon: Icons.logout_rounded,
          label: 'تسجيل الخروج',
          color: Colors.red,
          isDestructive: true,
          onTap: () async {
            final confirm = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('تأكيد تسجيل الخروج'),
                content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('إلغاء'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: const Text('تسجيل الخروج'),
                  ),
                ],
              ),
            );

            if (confirm == true && mounted) {
              try {
                // إظهار مؤشر التحميل
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) =>
                      const Center(child: CircularProgressIndicator()),
                );

                await Supabase.instance.client.auth.signOut();
                final prefs = await SharedPreferences.getInstance();
                await prefs.clear();

                if (mounted) {
                  // إغلاق مؤشر التحميل
                  Navigator.of(context).pop();
                  // العودة للشاشة الرئيسية
                  Navigator.of(
                    context,
                    rootNavigator: true,
                  ).pushNamedAndRemoveUntil('/', (route) => false);
                }
              } catch (e) {
                if (mounted) {
                  // إغلاق مؤشر التحميل في حالة الخطأ
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في تسجيل الخروج: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            }
          },
        ),
      ],
    );
  }

  // بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        style: ElevatedButton.styleFrom(
          backgroundColor: isDestructive ? color.withValues(alpha: 0.1) : color,
          foregroundColor: isDestructive ? color : Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: isDestructive
                ? BorderSide(color: color.withValues(alpha: 0.3))
                : BorderSide.none,
          ),
          elevation: isDestructive ? 0 : 2,
        ),
        icon: Icon(icon, size: 22),
        label: Text(
          label,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        onPressed: onTap,
      ),
    );
  }
}
