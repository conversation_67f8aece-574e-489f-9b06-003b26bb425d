# إعداد Firebase Storage للنسخ الاحتياطي

## 🔧 المشكلة الحالية:
خطأ 404 في Firebase Storage يشير إلى مشكلة في قواعد الأمان أو إعدادات المشروع.

## ✅ الحلول المطلوبة:

### 1. تحديث قواعد Firebase Storage

انتقل إلى Firebase Console → Storage → Rules وضع هذه القواعد:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // السماح للمستخدمين المسجلين بقراءة وكتابة ملفاتهم فقط
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد خاصة لملفات النسخ الاحتياطي
    match /itower_{userId}_{timestamp}.zip {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### 2. قواعد أبسط للاختبار (مؤقتة):

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 3. التحقق من إعدادات المشروع:

1. **تفعيل Firebase Storage:**
   - انتقل إلى Firebase Console
   - اختر مشروعك
   - انتقل إلى Storage
   - اضغط "Get Started"

2. **التحقق من Authentication:**
   - تأكد من تفعيل Google Sign-In
   - تأكد من تسجيل دخول المستخدم

3. **التحقق من الصلاحيات:**
   - تأكد من أن المستخدم مسجل دخول
   - تحقق من معرف المستخدم في الكونسول

### 4. اختبار الإعداد:

```dart
// اختبار بسيط لرفع ملف
final storageRef = FirebaseStorage.instance.ref().child('test.txt');
await storageRef.putString('Hello World');
```

## 🚀 التحديثات المطبقة في الكود:

### 1. تبسيط مسار التخزين:
- **قبل:** `backups/userId/filename.zip`
- **بعد:** `itower_userId_timestamp.zip`

### 2. معالجة أخطاء 404:
- إرجاع قائمة فارغة بدلاً من رمي خطأ
- تسجيل مفصل للعمليات

### 3. تصفية الملفات:
- عرض ملفات المستخدم الحالي فقط
- تجنب تداخل ملفات المستخدمين

## 📋 خطوات الاختبار:

1. **تطبيق قواعد Firebase الجديدة**
2. **تسجيل دخول المستخدم**
3. **اختبار رفع نسخة احتياطية**
4. **التحقق من Firebase Console**

## 🔍 تشخيص المشاكل:

### إذا استمر خطأ 404:
1. تحقق من قواعد Storage
2. تأكد من تسجيل الدخول
3. تحقق من صلاحيات المشروع
4. جرب القواعد المؤقتة البسيطة

### إذا فشل الرفع:
1. تحقق من حجم الملف (حد أقصى 32MB)
2. تأكد من الاتصال بالإنترنت
3. تحقق من مساحة Firebase المتاحة

## 📞 الدعم:
إذا استمرت المشاكل، تحقق من:
- Firebase Console → Storage → Usage
- Firebase Console → Authentication → Users
- Firebase Console → Project Settings → Service Accounts
