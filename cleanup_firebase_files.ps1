# سكريبت PowerShell لتنظيف ملفات Firebase غير المطلوبة
# تشغيل هذا السكريبت بعد التأكد من عمل التطبيق بـ Supabase

Write-Host "🧹 بدء تنظيف ملفات Firebase..." -ForegroundColor Green

# 1. حذف ملفات إعداد Firebase
Write-Host "📁 حذف ملفات إعداد Firebase..." -ForegroundColor Yellow

$googleServicesPath = "android\app\google-services.json"
if (Test-Path $googleServicesPath) {
    Remove-Item $googleServicesPath -Force
    Write-Host "✅ تم حذف google-services.json" -ForegroundColor Green
} else {
    Write-Host "ℹ️  google-services.json غير موجود" -ForegroundColor Blue
}

$googleServiceInfoPath = "ios\Runner\GoogleService-Info.plist"
if (Test-Path $googleServiceInfoPath) {
    Remove-Item $googleServiceInfoPath -Force
    Write-Host "✅ تم حذف GoogleService-Info.plist" -ForegroundColor Green
} else {
    Write-Host "ℹ️  GoogleService-Info.plist غير موجود" -ForegroundColor Blue
}

# 2. حذف ملفات وثائق Firebase
Write-Host "📄 حذف ملفات وثائق Firebase..." -ForegroundColor Yellow

$filesToRemove = @(
    "firebase_security_rules.md",
    "firebase_storage_rules_simple.txt",
    "FIREBASE_STORAGE_SETUP.md",
    "firebase_rules.txt"
)

foreach ($file in $filesToRemove) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "✅ تم حذف $file" -ForegroundColor Green
    } else {
        Write-Host "ℹ️  $file غير موجود" -ForegroundColor Blue
    }
}

# 3. حذف مجلدات Firebase إذا كانت موجودة
Write-Host "📂 حذف مجلدات Firebase..." -ForegroundColor Yellow

if (Test-Path "firebase") {
    Remove-Item "firebase" -Recurse -Force
    Write-Host "✅ تم حذف مجلد firebase" -ForegroundColor Green
} else {
    Write-Host "ℹ️  مجلد firebase غير موجود" -ForegroundColor Blue
}

# 4. تنظيف cache Flutter
Write-Host "🔄 تنظيف cache Flutter..." -ForegroundColor Yellow
flutter clean
Write-Host "✅ تم تنظيف cache Flutter" -ForegroundColor Green

# 5. إعادة تحميل dependencies
Write-Host "📦 إعادة تحميل dependencies..." -ForegroundColor Yellow
flutter pub get
Write-Host "✅ تم تحميل dependencies" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 تم الانتهاء من تنظيف ملفات Firebase!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 ملخص التغييرات:" -ForegroundColor Cyan
Write-Host "   ✅ تم إزالة جميع ملفات إعداد Firebase" -ForegroundColor Green
Write-Host "   ✅ تم إزالة جميع ملفات وثائق Firebase" -ForegroundColor Green
Write-Host "   ✅ تم تنظيف cache Flutter" -ForegroundColor Green
Write-Host "   ✅ تم إعادة تحميل dependencies" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 التطبيق الآن يعتمد كلياً على Supabase!" -ForegroundColor Magenta
Write-Host ""
Write-Host "⚠️  تذكير: تأكد من تشغيل التطبيق واختباره قبل حذف هذا السكريبت" -ForegroundColor Red
