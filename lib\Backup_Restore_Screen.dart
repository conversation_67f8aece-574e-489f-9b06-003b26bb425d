import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import 'dart:convert';
import 'package:permission_handler/permission_handler.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
import 'package:shared_preferences/shared_preferences.dart';
import 'services/supabase_backup_service.dart';
import 'services/daily_sync_service.dart';
import 'services/local_backup_service.dart';
import 'db_helper.dart';
import 'features/subscribers/data/subscriber_model.dart';
import 'features/subscribers/data/transaction_model.dart' as app_transaction;

// كلاس لتمثيل ملف النسخة الاحتياطية
class BackupFile {
  final String name;
  final String path;
  final DateTime createdAt;
  final int size;

  BackupFile({
    required this.name,
    required this.path,
    required this.createdAt,
    required this.size,
  });

  String get formattedDate => DateFormat('yyyy-MM-dd HH:mm').format(createdAt);
  String get formattedSize => '${(size / 1024 / 1024).toStringAsFixed(2)} MB';
}

class BackupRestoreScreen extends StatefulWidget {
  const BackupRestoreScreen({super.key});

  @override
  State<BackupRestoreScreen> createState() => _BackupRestoreScreenState();
}

class _BackupRestoreScreenState extends State<BackupRestoreScreen> {
  bool _isBackupLoading = false;
  bool _isRestoreLoading = false;
  String? _statusMessage;
  List<BackupFile> _availableBackups = [];

  // خدمات النسخ الاحتياطي
  final LocalBackupService _localBackup = LocalBackupService(); // النسخ المحلي
  final SupabaseBackupService _cloudBackup =
      SupabaseBackupService(); // النسخ السحابي
  final DailySyncService _dailySync = DailySyncService(); // المزامنة اليومية

  // متغيرات النسخ السحابي
  bool _isCloudBackupLoading = false;

  List<SupabaseBackupInfo> _cloudBackups = [];
  bool _isLoadingCloudBackups = false;

  DailySyncStats? _syncStats;
  bool _isDailySyncEnabled = true;

  Future<void> _initializeData() async {
    await _loadAvailableBackups();
    await _loadCloudBackups();
    await _loadSyncStats();
    await _loadDailySyncSetting();
  }

  Future<String> getDatabasePath() async {
    // اسم قاعدة البيانات الفعلي في المشروع
    final dbPath = await sqflite.getDatabasesPath();
    final fullPath = '$dbPath/itower.db';

    // تسجيل مفصل لتتبع المسار
    print('مسار قاعدة البيانات: $fullPath');

    // التحقق من وجود الملف
    final file = File(fullPath);
    final exists = await file.exists();
    print('هل يوجد ملف قاعدة البيانات؟ $exists');

    if (exists) {
      final size = await file.length();
      print('حجم قاعدة البيانات: ${(size / 1024).toStringAsFixed(2)} KB');
    }

    return fullPath;
  }

  Future<void> _backupDatabase() async {
    _safeSetState(() {
      _isBackupLoading = true;
      _statusMessage = 'جاري إنشاء النسخة الاحتياطية المحلية...';
    });

    try {
      // طلب إذن الكتابة
      await Permission.storage.request();

      _safeSetState(() {
        _statusMessage =
            'جاري إنشاء النسخة الاحتياطية باستخدام الخدمة المحلية...';
      });

      // استخدام خدمة النسخ المحلي المنفصلة
      final result = await _localBackup.createLocalBackup();

      if (result.success) {
        _safeSetState(() {
          _statusMessage = 'جاري مشاركة النسخة الاحتياطية...';
        });

        // مشاركة الملف
        await Share.shareXFiles([
          XFile(result.filePath),
        ], text: 'نسخة احتياطية محلية من تطبيق iTower - ${result.fileName}');

        _safeSetState(() {
          _statusMessage =
              'تم إنشاء النسخة الاحتياطية المحلية بنجاح! ✅\n\n'
              '📄 الملف: ${result.fileName}\n'
              '💾 النوع: نسخة قاعدة بيانات محلية\n'
              '📁 الحجم: ${_localBackup.formatFileSize(result.fileSize)}\n'
              '📍 المسار: ${result.filePath}\n'
              '🕒 التاريخ: ${DateFormat('yyyy-MM-dd HH:mm').format(result.createdAt)}';
          _isBackupLoading = false;
        });
      } else {
        _safeSetState(() {
          _statusMessage =
              'فشل في إنشاء النسخة الاحتياطية المحلية:\n${result.message}';
          _isBackupLoading = false;
        });
      }

      // تحديث قائمة النسخ الاحتياطية
      await _loadAvailableBackups();
    } catch (e) {
      _safeSetState(() {
        _statusMessage = 'حدث خطأ أثناء النسخ الاحتياطي:\n$e';
        _isBackupLoading = false;
      });
    }
  }

  // استيراد البيانات من JSON
  Future<void> _importDataFromJson(Map<String, dynamic> backupData) async {
    final dbHelper = DBHelper.instance;

    try {
      // التحقق من صحة البيانات
      if (!backupData.containsKey('data') ||
          !backupData.containsKey('version')) {
        throw Exception('ملف النسخة الاحتياطية غير صالح');
      }

      final data = backupData['data'] as Map<String, dynamic>;

      // مسح البيانات الحالية (إعادة تعيين قاعدة البيانات)
      await dbHelper.resetDatabase();

      // استيراد المشتركين
      if (data.containsKey('subscribers')) {
        final subscribers = data['subscribers'] as List;
        for (final subscriberData in subscribers) {
          final subscriber = Subscriber.fromMap(
            subscriberData as Map<String, dynamic>,
          );
          await dbHelper.insertSubscriber(subscriber);
        }
      }

      // استيراد المعاملات
      if (data.containsKey('transactions')) {
        final transactions = data['transactions'] as List;
        for (final transactionData in transactions) {
          final transaction = app_transaction.Transaction.fromMap(
            transactionData as Map<String, dynamic>,
          );
          await dbHelper.insertTransaction(transaction);
        }
      }

      // استيراد الأجهزة
      if (data.containsKey('devices')) {
        final devices = data['devices'] as List;
        for (final deviceData in devices) {
          await dbHelper.insertDevice(deviceData as Map<String, dynamic>);
        }
      }
    } catch (e) {
      throw Exception('فشل في استيراد البيانات: $e');
    }
  }

  // تحميل النسخ الاحتياطية المتاحة
  Future<void> _loadAvailableBackups() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final backupStrings = prefs.getStringList('backup_files') ?? [];

      final backups = <BackupFile>[];
      for (final backupString in backupStrings) {
        final parts = backupString.split('|');
        if (parts.length == 4) {
          final file = File(parts[1]);
          if (await file.exists()) {
            backups.add(
              BackupFile(
                name: parts[0],
                path: parts[1],
                createdAt: DateTime.fromMillisecondsSinceEpoch(
                  int.parse(parts[2]),
                ),
                size: int.parse(parts[3]),
              ),
            );
          }
        }
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      backups.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      _safeSetState(() {
        _availableBackups = backups;
      });
    } catch (e) {}
  }

  // استعادة من نسخة احتياطية محددة
  Future<void> _restoreFromBackup(BackupFile backup) async {
    // تحديد نوع الملف
    final isJsonBackup = backup.name.endsWith('.json');

    // تأكيد من المستخدم
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: Text(
          'هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\n'
          'الملف: ${backup.name}\n'
          'النوع: ${isJsonBackup ? 'JSON' : 'قاعدة بيانات'}\n'
          'التاريخ: ${backup.formattedDate}\n'
          'الحجم: ${backup.formattedSize}\n\n'
          'تحذير: سيتم استبدال جميع البيانات الحالية!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('استعادة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    _safeSetState(() {
      _isRestoreLoading = true;
      _statusMessage = isJsonBackup
          ? 'جاري استيراد البيانات من JSON...'
          : 'جاري استعادة قاعدة البيانات...';
    });

    try {
      final backupFile = File(backup.path);
      if (!await backupFile.exists()) {
        _safeSetState(() {
          _statusMessage = 'ملف النسخة الاحتياطية غير موجود.';
          _isRestoreLoading = false;
        });
        return;
      }

      if (isJsonBackup) {
        // استعادة من JSON
        _safeSetState(() {
          _statusMessage = 'جاري قراءة ملف JSON...';
        });

        final jsonString = await backupFile.readAsString(encoding: utf8);
        final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

        setState(() {
          _statusMessage = 'جاري استيراد البيانات...';
        });

        await _importDataFromJson(backupData);

        setState(() {
          _statusMessage =
              'تم استيراد البيانات من JSON بنجاح! ✅\n\n'
              '📊 البيانات المستوردة:\n'
              '• المشتركين: ${backupData['metadata']?['total_subscribers'] ?? 0}\n'
              '• المعاملات: ${backupData['metadata']?['total_transactions'] ?? 0}\n'
              '• الأجهزة: ${backupData['metadata']?['total_devices'] ?? 0}\n\n'
              'أعد تشغيل التطبيق لرؤية التغييرات.';
          _isRestoreLoading = false;
        });
      } else {
        // استعادة قاعدة البيانات التقليدية
        final dbPath = await getDatabasePath();
        await backupFile.copy(dbPath);

        setState(() {
          _statusMessage =
              'تمت الاستعادة بنجاح! أعد تشغيل التطبيق لرؤية التغييرات.';
          _isRestoreLoading = false;
        });
      }

      // إعادة تشغيل التطبيق بعد 3 ثوان
      await Future.delayed(const Duration(seconds: 3));
      SystemNavigator.pop();
    } catch (e) {
      setState(() {
        _statusMessage = 'حدث خطأ أثناء الاستعادة:\n$e';
        _isRestoreLoading = false;
      });
    }
  }

  // حذف نسخة احتياطية
  Future<void> _deleteBackup(BackupFile backup) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف النسخة الاحتياطية'),
        content: Text('هل أنت متأكد من حذف:\n${backup.name}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final file = File(backup.path);
      if (await file.exists()) {
        await file.delete();
      }

      // إزالة من القائمة المحفوظة
      final prefs = await SharedPreferences.getInstance();
      final backups = prefs.getStringList('backup_files') ?? [];
      backups.removeWhere((b) => b.contains(backup.name));
      await prefs.setStringList('backup_files', backups);

      await _loadAvailableBackups();

      setState(() {
        _statusMessage = 'تم حذف النسخة الاحتياطية بنجاح.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'حدث خطأ أثناء الحذف: $e';
      });
    }
  }

  // تسجيل الدخول بـ Google
  Future<void> _signInWithGoogle() async {
    try {
      // هنا يجب إضافة منطق تسجيل الدخول بـ Google
      // يمكن استخدام google_sign_in package
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تسجيل الدخول من الإعدادات أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تسجيل الدخول: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // استعادة نسخة احتياطية من السحابة
  Future<void> _restoreFromCloud(SupabaseBackupInfo backup) async {
    // تأكيد الاستعادة
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟'),
            const SizedBox(height: 16),
            Text('اسم الملف: ${backup.name}'),
            Text('تاريخ الإنشاء: ${backup.createdAt.toString().split('.')[0]}'),
            Text('الحجم: ${_formatFileSize(backup.size)}'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تحذير: سيتم استبدال جميع البيانات الحالية!',
                      style: TextStyle(
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('استعادة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _statusMessage = 'جاري تحميل النسخة الاحتياطية من السحابة...';
    });

    try {
      // الحصول على مسار قاعدة البيانات
      final dbPath = await getDatabasePath();

      // تحميل واستعادة النسخة الاحتياطية
      await _cloudBackup.downloadAndRestoreBackup(backup.name, dbPath);

      setState(() {
        _statusMessage =
            'تم تحميل واستعادة النسخة الاحتياطية بنجاح!\n'
            'الملف: ${backup.name}\n'
            'تاريخ الإنشاء: ${backup.createdAt.toString().split('.')[0]}';
      });

      // إعادة تشغيل التطبيق لتطبيق التغييرات
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('تم الاستعادة بنجاح'),
            content: const Text('يجب إعادة تشغيل التطبيق لتطبيق التغييرات.'),
            actions: [
              ElevatedButton(
                onPressed: () {
                  // إغلاق التطبيق
                  SystemNavigator.pop();
                },
                child: const Text('إعادة تشغيل'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'حدث خطأ أثناء استعادة النسخة الاحتياطية:\n$e';
      });
    }
  }

  // تحميل النسخ الاحتياطية السحابية
  Future<void> _loadCloudBackups() async {
    setState(() {
      _isLoadingCloudBackups = true;
    });

    try {
      final cloudBackups = await _cloudBackup.getCloudBackups();
      setState(() {
        _cloudBackups = cloudBackups;
        _isLoadingCloudBackups = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCloudBackups = false;
      });
      print('خطأ في تحميل النسخ السحابية: $e');
    }
  }

  // تحميل إحصائيات المزامنة
  Future<void> _loadSyncStats() async {
    try {
      final stats = await _dailySync.getSyncStats();
      setState(() {
        _syncStats = stats;
      });
    } catch (e) {
      print('خطأ في تحميل إحصائيات المزامنة: $e');
    }
  }

  // تحميل إعداد المزامنة اليومية
  Future<void> _loadDailySyncSetting() async {
    try {
      final isEnabled = await _dailySync.isDailySyncEnabled();
      setState(() {
        _isDailySyncEnabled = isEnabled;
      });
    } catch (e) {
      print('خطأ في تحميل إعداد المزامنة اليومية: $e');
    }
  }

  // تغيير إعداد المزامنة اليومية
  Future<void> _toggleDailySync(bool enabled) async {
    try {
      await _dailySync.setDailySyncEnabled(enabled);
      setState(() {
        _isDailySyncEnabled = enabled;
      });

      // إظهار رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              enabled
                  ? 'تم تفعيل المزامنة التلقائية اليومية'
                  : 'تم إيقاف المزامنة التلقائية اليومية',
            ),
            backgroundColor: enabled ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // تحديث الإحصائيات
      await _loadSyncStats();
    } catch (e) {
      print('خطأ في تغيير إعداد المزامنة اليومية: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الإعداد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // تنفيذ مزامنة يدوية
  Future<void> _performManualSync() async {
    // استخدام دالة النسخ الاحتياطي السحابي مباشرة
    await _performCloudBackup();
  }

  // نسخ احتياطي سحابي مع إدارة الحد الأقصى
  Future<void> _performCloudBackup() async {
    setState(() {
      _isCloudBackupLoading = true;
      _statusMessage = 'جاري إنشاء النسخة الاحتياطية...';
    });

    try {
      // التحقق من وجود قاعدة البيانات
      final dbPath = await getDatabasePath();
      final dbFile = File(dbPath);

      if (!await dbFile.exists()) {
        setState(() {
          _statusMessage =
              'لم يتم العثور على قاعدة البيانات في المسار: $dbPath';
          _isCloudBackupLoading = false;
        });
        return;
      }

      setState(() {
        _statusMessage = 'جاري فحص النسخ الاحتياطية الموجودة...';
      });

      // الحصول على النسخ الاحتياطية الحالية
      final existingBackups = await _cloudBackup.getCloudBackups();

      setState(() {
        _statusMessage =
            'تم العثور على ${existingBackups.length} نسخة احتياطية موجودة';
      });

      // إذا كان عدد النسخ 5 أو أكثر، احذف الأقدم
      if (existingBackups.length >= 5) {
        setState(() {
          _statusMessage = 'حذف النسخ القديمة (الحد الأقصى 5 نسخ)...';
        });

        // ترتيب النسخ حسب التاريخ (الأقدم أولاً)
        existingBackups.sort((a, b) => a.createdAt.compareTo(b.createdAt));

        // حذف النسخ الزائدة (نحتفظ بـ 4 نسخ لإضافة الجديدة)
        final backupsToDelete = existingBackups
            .take(existingBackups.length - 4)
            .toList();

        for (final backup in backupsToDelete) {
          try {
            final userId = await _cloudBackup.currentUserId;
            await _cloudBackup.deleteCloudBackup(
              backup.name,
              '$userId/${backup.name}',
            );
            print('تم حذف النسخة القديمة: ${backup.name}');
          } catch (e) {
            print('خطأ في حذف النسخة القديمة ${backup.name}: $e');
          }
        }
      }

      setState(() {
        _statusMessage = 'جاري رفع النسخة الاحتياطية الجديدة...';
      });

      // تصدير البيانات للنسخ السحابي باستخدام الخدمة المنفصلة
      final backupData = await _cloudBackup.exportDataForCloudBackup();

      // رفع النسخة الاحتياطية JSON الجديدة
      final result = await _cloudBackup.uploadJsonBackup(backupData);

      if (result.success) {
        setState(() {
          _statusMessage =
              'تم رفع النسخة الاحتياطية للسحابة بنجاح! ✅\n\n'
              '📊 تفاصيل النسخة:\n'
              '• حجم الملف: ${(result.fileSize / 1024).toStringAsFixed(2)} KB\n'
              '• اسم الملف: ${result.fileName}\n'
              '• التاريخ: ${DateTime.fromMillisecondsSinceEpoch(result.uploadTimestamp).toString().split('.')[0]}';
          _isCloudBackupLoading = false;
        });

        // تحديث القوائم
        await _loadCloudBackups();
        await _loadSyncStats();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إنشاء النسخة الاحتياطية السحابية بنجاح!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('فشل في رفع النسخة الاحتياطية');
      }
    } catch (e) {
      setState(() {
        _statusMessage =
            'حدث خطأ أثناء النسخ الاحتياطي السحابي:\n\n'
            '❌ تفاصيل الخطأ:\n$e\n\n'
            '💡 تأكد من:\n'
            '• الاتصال بالإنترنت\n'
            '• تسجيل الدخول بحساب Google\n'
            '• وجود مساحة كافية في التخزين السحابي';
        _isCloudBackupLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في النسخ الاحتياطي: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // عرض قائمة النسخ الاحتياطية في نافذة منبثقة
  void _showBackupsList() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) => Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.history,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'النسخ الاحتياطية المتاحة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            Expanded(
              child: _availableBackups.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.folder_open, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('لا توجد نسخ احتياطية'),
                        ],
                      ),
                    )
                  : ListView.builder(
                      controller: scrollController,
                      itemCount: _availableBackups.length,
                      itemBuilder: (context, index) {
                        final backup = _availableBackups[index];
                        return _buildBackupListItem(backup);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupListItem(BackupFile backup) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: colorScheme.primary.withValues(alpha: 0.1),
          child: Icon(Icons.storage, color: colorScheme.primary),
        ),
        title: Text(
          backup.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('التاريخ: ${backup.formattedDate}'),
            Text('الحجم: ${backup.formattedSize}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) async {
            Navigator.pop(context); // إغلاق القائمة المنبثقة
            switch (value) {
              case 'restore':
                await _restoreFromBackup(backup);
                break;
              case 'share':
                await Share.shareXFiles([XFile(backup.path)]);
                break;
              case 'delete':
                await _deleteBackup(backup);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'restore',
              child: Row(
                children: [
                  Icon(Icons.restore, color: Colors.green),
                  SizedBox(width: 8),
                  Text('استعادة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('مشاركة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    // إلغاء أي عمليات غير متزامنة قيد التشغيل
    super.dispose();
  }

  // دالة مساعدة لـ setState الآمن
  void _safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withAlpha(230),
                        colorScheme.surface.withAlpha(217),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // قسم النسخ المحلي
                  _buildLocalBackupSection(colorScheme, isDark),
                  const SizedBox(height: 24),

                  // قسم المزامنة السحابية - Firebase
                  _buildModernCloudSyncSection(colorScheme, isDark),
                  const SizedBox(height: 24),

                  // قسم النسخ السحابي - Supabase
                  _buildSupabaseBackupSection(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // مؤشر التحميل والرسائل
                  _buildStatusSection(colorScheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withAlpha(46),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.white.withAlpha(isDark ? 20 : 46),
            child: Icon(
              Icons.backup_rounded,
              color: colorScheme.primary,
              size: 54,
            ),
          ),
        ),
        // عنوان الشاشة
        Text(
          'النسخ الاحتياطي',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(color: colorScheme.shadow.withAlpha(33), blurRadius: 4),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'حماية بياناتك مع النسخ المحلي والسحابي',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withAlpha(235),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء قسم النسخ المحلي
  Widget _buildLocalBackupSection(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withAlpha(isDark ? 179 : 237),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.storage_rounded,
                  color: colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'النسخ المحلي',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'إنشاء واستعادة النسخ الاحتياطية على جهازك',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withAlpha(179),
              ),
            ),
            const SizedBox(height: 20),

            // أزرار النسخ المحلي
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.save_alt_rounded, size: 20),
                    label: const Text('إنشاء نسخة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      foregroundColor: colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                      textStyle: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onPressed: _isBackupLoading ? null : _backupDatabase,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.restore_rounded, size: 20),
                    label: const Text('استعادة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.secondary,
                      foregroundColor: colorScheme.onSecondary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                      textStyle: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onPressed: _availableBackups.isEmpty
                        ? null
                        : () => _showBackupsList(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم المزامنة السحابية العصري
  Widget _buildModernCloudSyncSection(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withAlpha(isDark ? 179 : 237),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.cloud_sync_rounded,
                  color: colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'المزامنة السحابية',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'نسخ احتياطي تلقائي يومي مضغوط وآمن في السحابة',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withAlpha(179),
              ),
            ),
            const SizedBox(height: 20),

            // مفتاح تفعيل/إيقاف المزامنة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: _isDailySyncEnabled
                      ? [
                          colorScheme.primaryContainer.withAlpha(102),
                          colorScheme.primaryContainer.withAlpha(51),
                        ]
                      : [
                          colorScheme.surfaceContainerHighest.withAlpha(102),
                          colorScheme.surfaceContainerHighest.withAlpha(51),
                        ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _isDailySyncEnabled
                      ? colorScheme.primary.withAlpha(77)
                      : colorScheme.outline.withAlpha(77),
                  width: 1.5,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _isDailySyncEnabled ? Colors.green : Colors.grey,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _isDailySyncEnabled
                          ? Icons.cloud_done_rounded
                          : Icons.cloud_off_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المزامنة التلقائية اليومية',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          _isDailySyncEnabled
                              ? 'مفعلة - سيتم إنشاء نسخة احتياطية تلقائياً كل يوم'
                              : 'معطلة - لن يتم إنشاء نسخ احتياطية تلقائية',
                          style: TextStyle(
                            fontSize: 13,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                            height: 1.3,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch.adaptive(
                    value: _isDailySyncEnabled,
                    onChanged: _toggleDailySync,
                    activeColor: Colors.green,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ],
              ),
            ),

            // إحصائيات المزامنة
            if (_syncStats != null) ...[
              const SizedBox(height: 20),
              _buildModernSyncStats(colorScheme),
            ],

            // أزرار العمليات
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.sync_rounded, size: 20),
                    label: const Text('مزامنة الآن'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.tertiary,
                      foregroundColor: colorScheme.onTertiary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                      textStyle: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onPressed: _cloudBackup.isUserLoggedIn
                        ? _performManualSync
                        : null,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.cloud_download_rounded, size: 20),
                    label: const Text('عرض النسخ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.secondary,
                      foregroundColor: colorScheme.onSecondary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                      textStyle: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onPressed: _cloudBackup.isUserLoggedIn
                        ? _showCloudBackupsList
                        : null,
                  ),
                ),
              ],
            ),

            // رسالة تسجيل الدخول
            if (!_cloudBackup.isUserLoggedIn) ...[
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.withValues(alpha: 0.15),
                      Colors.orange.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.info_outline_rounded,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'يجب تسجيل الدخول لاستخدام المزامنة السحابية',
                            style: TextStyle(
                              color: Colors.orange.shade700,
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            onPressed: _signInWithGoogle,
                            icon: const Icon(Icons.login, size: 16),
                            label: const Text('تسجيل الدخول'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              textStyle: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // بناء قسم Supabase للنسخ السحابي
  Widget _buildSupabaseBackupSection(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withAlpha(isDark ? 179 : 237),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان مع الأيقونة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.green.withValues(alpha: 0.8),
                        Colors.green.withValues(alpha: 0.6),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.cloud_sync,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'النسخ السحابي - Supabase',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'نسخ احتياطي سريع وآمن',
                        style: TextStyle(
                          fontSize: 14,
                          color: colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // الأزرار
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: _isCloudBackupLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Icon(Icons.cloud_upload_rounded, size: 20),
                    label: Text(
                      _isCloudBackupLoading ? 'جاري الرفع...' : 'نسخ احتياطي',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                      textStyle: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onPressed: _isCloudBackupLoading
                        ? null
                        : _performCloudBackup,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.cloud_download_rounded, size: 20),
                    label: const Text('عرض النسخ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.secondary,
                      foregroundColor: colorScheme.onSecondary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                      textStyle: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onPressed: _showCloudBackupsList,
                  ),
                ),
              ],
            ),

            // النسخ السحابي متاح دائماً
          ],
        ),
      ),
    );
  }

  // بناء إحصائيات المزامنة العصرية
  Widget _buildModernSyncStats(ColorScheme colorScheme) {
    final stats = _syncStats!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.surfaceContainerHighest.withValues(alpha: 0.4),
            colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          _buildStatRow(
            'آخر مزامنة:',
            stats.formattedLastSync,
            Icons.schedule_rounded,
            colorScheme,
          ),
          const SizedBox(height: 12),
          _buildStatRow(
            'المزامنات:',
            '${stats.totalSyncs}',
            Icons.sync_alt_rounded,
            colorScheme,
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    size: 16,
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'الحالة:',
                    style: TextStyle(
                      fontSize: 13,
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: stats.lastSyncSuccess
                        ? [Colors.green, Colors.green.shade600]
                        : [Colors.red, Colors.red.shade600],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: (stats.lastSyncSuccess ? Colors.green : Colors.red)
                          .withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  stats.statusText,
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء صف إحصائية
  Widget _buildStatRow(
    String label,
    String value,
    IconData icon,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: colorScheme.onSurface.withValues(alpha: 0.7),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 8),
        Flexible(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
            textAlign: TextAlign.end,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // بناء قسم الحالة والرسائل
  Widget _buildStatusSection(ColorScheme colorScheme) {
    return Column(
      children: [
        // مؤشر التحميل
        if (_isBackupLoading || _isRestoreLoading || _isCloudBackupLoading)
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: colorScheme.surface.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: [
                CircularProgressIndicator(
                  color: colorScheme.primary,
                  strokeWidth: 3,
                ),
                const SizedBox(height: 12),
                Text(
                  'جاري المعالجة...',
                  style: TextStyle(
                    color: colorScheme.onSurface.withValues(alpha: 0.8),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

        // رسائل الحالة
        if (_statusMessage != null) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _statusMessage!.contains('نجاح')
                    ? [
                        Colors.green.withValues(alpha: 0.15),
                        Colors.green.withValues(alpha: 0.05),
                      ]
                    : [
                        Colors.red.withValues(alpha: 0.15),
                        Colors.red.withValues(alpha: 0.05),
                      ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color:
                    (_statusMessage!.contains('نجاح')
                            ? Colors.green
                            : Colors.red)
                        .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _statusMessage!.contains('نجاح')
                        ? Colors.green
                        : Colors.red,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _statusMessage!.contains('نجاح')
                        ? Icons.check_circle_outline_rounded
                        : Icons.error_outline_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _statusMessage!,
                    style: TextStyle(
                      color:
                          (_statusMessage!.contains('نجاح')
                                  ? Colors.green
                                  : Colors.red)
                              .shade700,
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  // حذف نسخة احتياطية من السحابة
  Future<void> _deleteCloudBackup(SupabaseBackupInfo backup) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف النسخة الاحتياطية "${backup.name}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final userId = await _cloudBackup.currentUserId;
        await _cloudBackup.deleteCloudBackup(
          backup.name,
          '$userId/${backup.name}',
        );
        await _loadCloudBackups(); // إعادة تحميل القائمة
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف النسخة الاحتياطية "${backup.name}"'),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف النسخة الاحتياطية: $e')),
          );
        }
      }
    }
  }

  // عرض قائمة النسخ السحابية
  void _showCloudBackupsList() {
    // تحديث النسخ السحابية قبل العرض
    _loadCloudBackups();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('النسخ الاحتياطية السحابية'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: _isLoadingCloudBackups
              ? const Center(child: CircularProgressIndicator())
              : _cloudBackups.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.cloud_off, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد نسخ احتياطية سحابية',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _cloudBackups.length,
                  itemBuilder: (context, index) {
                    final backup = _cloudBackups[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      child: ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.cloud, color: Colors.blue),
                        ),
                        title: Text(
                          backup.name,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'تاريخ الإنشاء: ${backup.createdAt.toString().split('.')[0]}',
                              style: const TextStyle(fontSize: 12),
                            ),
                            Text(
                              'الحجم: ${_formatFileSize(backup.size)}',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(
                                Icons.restore,
                                color: Colors.green,
                              ),
                              onPressed: () {
                                Navigator.of(context).pop();
                                _restoreFromCloud(backup);
                              },
                              tooltip: 'استعادة',
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _deleteCloudBackup(backup),
                              tooltip: 'حذف',
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: _loadCloudBackups,
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }
}
