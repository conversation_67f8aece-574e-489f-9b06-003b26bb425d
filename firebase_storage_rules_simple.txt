// Firebase Storage Rules - إعدادات بسيطة للسماح بالرفع

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // السماح للمستخدمين المسجلين برفع وتحميل الملفات
    match /{allPaths=**} {
      // السماح بالقراءة والكتابة للمستخدمين المسجلين فقط
      allow read, write: if request.auth != null;
    }
  }
}

// ملاحظات:
// 1. هذه القواعد بسيطة وتسمح لأي مستخدم مسجل بالوصول لجميع الملفات
// 2. يمكن تحسينها لاحقاً لتكون أكثر أماناً
// 3. تأكد من تطبيق هذه القواعد في Firebase Console > Storage > Rules

// خطوات التطبيق:
// 1. اذهب إلى Firebase Console
// 2. اختر مشروعك
// 3. اذهب إلى Storage
// 4. اختر Rules
// 5. انسخ والصق القواعد أعلاه
// 6. اضغط Publish

// بعد تطبيق هذه القواعد، يجب أن يعمل الرفع بشكل صحيح
