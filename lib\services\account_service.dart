import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class AccountService {
  static const String _cachePrefix = 'cache_';
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // نموذج بيانات الحساب
  static Map<String, dynamic> _defaultAccountData = {
    'is_trial': true,
    'expiry_millis': 0,
    'creation_millis': 0,
    'activation_millis': 0,
    'active_package': '',
    'display_name': '',
    'trial_days': 15,
  };

  // إنشاء حساب جديد في Supabase
  static Future<Map<String, dynamic>> createAccount(
    String userId, {
    String? displayName,
  }) async {
    try {
      debugPrint('🔍 [ACCOUNT] بدء إنشاء الحساب للمستخدم: $userId');
      debugPrint('🔍 [ACCOUNT] اسم المستخدم: $displayName');

      final now = DateTime.now();
      final expiryDate = now.add(const Duration(days: 15));
      debugPrint('🔍 [ACCOUNT] تاريخ الانتهاء: $expiryDate');

      final accountData = {
        'user_id': userId,
        'is_trial': true,
        'expiry_millis': expiryDate.millisecondsSinceEpoch,
        'creation_millis': now.millisecondsSinceEpoch,
        'activation_millis': 0,
        'active_package': '',
        'display_name': displayName ?? '',
        'trial_days': 15,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      // التحقق من صحة الجلسة وتجديدها إذا لزم الأمر
      debugPrint('🔍 [ACCOUNT] فحص الجلسة الحالية...');
      final currentUser = Supabase.instance.client.auth.currentUser;
      debugPrint('🔍 [ACCOUNT] المستخدم الحالي: ${currentUser?.id}');

      if (currentUser == null) {
        debugPrint('❌ [ACCOUNT] لا يوجد مستخدم مسجل دخول');
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      if (currentUser.id != userId) {
        debugPrint(
          '❌ [ACCOUNT] معرف المستخدم غير متطابق: ${currentUser.id} != $userId',
        );
        throw Exception('معرف المستخدم غير متطابق');
      }

      debugPrint('✅ [ACCOUNT] المستخدم متطابق: ${currentUser.id}');

      // التحقق من حالة الجلسة والاتصال
      try {
        debugPrint('🔍 [ACCOUNT] فحص حالة الجلسة...');
        final session = Supabase.instance.client.auth.currentSession;
        if (session == null) {
          debugPrint('❌ [ACCOUNT] لا توجد جلسة نشطة');
          throw Exception('لا توجد جلسة نشطة');
        }
        debugPrint('✅ [ACCOUNT] الجلسة نشطة ومتاحة للاستخدام');
        debugPrint(
          '🔍 [ACCOUNT] Access Token متاح: ${session.accessToken.isNotEmpty}',
        );
        debugPrint('🔍 [ACCOUNT] انتهاء الجلسة: ${session.expiresAt}');
        debugPrint('🔍 [ACCOUNT] الوقت الحالي: ${DateTime.now()}');

        // اختبار الاتصال بقاعدة البيانات
        try {
          debugPrint('🔍 [ACCOUNT] اختبار الاتصال بقاعدة البيانات...');
          final testResponse = await Supabase.instance.client
              .from('user_accounts')
              .select('count')
              .count(CountOption.exact);
          debugPrint(
            '✅ [ACCOUNT] اختبار الاتصال نجح - عدد السجلات: ${testResponse.count}',
          );
        } catch (testError) {
          debugPrint('❌ [ACCOUNT] فشل اختبار الاتصال: $testError');
          debugPrint('❌ [ACCOUNT] نوع خطأ الاتصال: ${testError.runtimeType}');
        }
      } catch (e) {
        debugPrint('مشكلة في الجلسة: $e');
        // محاولة تجديد الجلسة
        try {
          await Supabase.instance.client.auth.refreshSession();
          debugPrint('تم تجديد جلسة المستخدم بنجاح');
        } catch (refreshError) {
          debugPrint('فشل في تجديد الجلسة: $refreshError');
          throw Exception('فشل في الوصول للجلسة: $refreshError');
        }
      }

      // إدراج البيانات في Supabase مع إعادة المحاولة
      bool insertSuccess = false;
      int retryCount = 0;
      const maxRetries = 3;
      Exception? lastError;

      while (!insertSuccess && retryCount < maxRetries) {
        try {
          retryCount++;
          debugPrint('🔍 [ACCOUNT] محاولة إنشاء الحساب #$retryCount');
          debugPrint('🔍 [ACCOUNT] بيانات الحساب: $accountData');

          // فحص المستخدم الحالي مرة أخرى
          final currentUser = Supabase.instance.client.auth.currentUser;
          debugPrint(
            '🔍 [ACCOUNT] المستخدم الحالي قبل الإدراج: ${currentUser?.id}',
          );
          debugPrint('🔍 [ACCOUNT] المستخدم المطلوب: $userId');

          // فحص الجلسة مرة أخرى
          final sessionBeforeInsert =
              Supabase.instance.client.auth.currentSession;
          debugPrint(
            '🔍 [ACCOUNT] حالة الجلسة قبل الإدراج: ${sessionBeforeInsert != null ? "موجودة" : "غير موجودة"}',
          );

          if (sessionBeforeInsert != null) {
            final timeUntilExpiry =
                sessionBeforeInsert.expiresAt! -
                (DateTime.now().millisecondsSinceEpoch / 1000).round();
            debugPrint(
              '🔍 [ACCOUNT] الوقت المتبقي للجلسة: $timeUntilExpiry ثانية',
            );
          }

          debugPrint('🔍 [ACCOUNT] بدء عملية الإدراج...');
          final response = await Supabase.instance.client
              .from('user_accounts')
              .insert(accountData)
              .select();

          debugPrint('✅ [ACCOUNT] استجابة الإدراج: $response');
          insertSuccess = true;
          debugPrint(
            '✅ [ACCOUNT] تم إنشاء حساب جديد في Supabase للمستخدم: $userId',
          );
        } catch (e) {
          debugPrint('خطأ في محاولة #$retryCount: $e');
          debugPrint('نوع الخطأ: ${e.runtimeType}');
          if (e is PostgrestException) {
            debugPrint(
              'PostgrestException - Code: ${e.code}, Message: ${e.message}',
            );
            debugPrint('Details: ${e.details}');
            debugPrint('Hint: ${e.hint}');
          }
          lastError = Exception(e.toString());

          if (e.toString().contains(
            'relation "user_accounts" does not exist',
          )) {
            debugPrint('❌ جدول user_accounts غير موجود في Supabase');
            debugPrint(
              '📋 يرجى تنفيذ الكود التالي في Supabase Dashboard > SQL Editor:',
            );
            debugPrint('CREATE TABLE user_accounts (...);');
            debugPrint('ALTER TABLE user_accounts ENABLE ROW LEVEL SECURITY;');
            debugPrint('CREATE POLICY ... ON user_accounts;');
            break; // لا فائدة من إعادة المحاولة
          } else if (e.toString().contains('permission denied') ||
              e.toString().contains('insufficient_privilege')) {
            debugPrint('❌ مشكلة في صلاحيات قاعدة البيانات');
            debugPrint('📋 تحقق من Row Level Security policies في Supabase');
            break; // لا فائدة من إعادة المحاولة
          } else if (e.toString().contains('violates row-level security')) {
            debugPrint('❌ مشكلة في Row Level Security');
            debugPrint('📋 تحقق من سياسات الأمان في Supabase Dashboard');
            break; // لا فائدة من إعادة المحاولة
          } else if (e.toString().contains('duplicate key value')) {
            debugPrint('⚠️ المستخدم موجود مسبقاً في user_accounts');
            // جلب البيانات الموجودة بدلاً من إنشاء جديدة
            final existingData = await getAccountData(userId, useCache: false);
            if (existingData != null) {
              return existingData;
            }
            break; // لا فائدة من إعادة المحاولة
          } else if (e.toString().contains('Auth session missing') &&
              retryCount < maxRetries) {
            debugPrint('⚠️ جلسة منتهية، محاولة تجديد الجلسة...');
            try {
              await Supabase.instance.client.auth.refreshSession();
              await Future.delayed(
                Duration(milliseconds: 1000 * retryCount),
              ); // تأخير أطول
              debugPrint('تم تجديد الجلسة، إعادة المحاولة...');
            } catch (refreshError) {
              debugPrint('فشل في تجديد الجلسة: $refreshError');
              break; // إذا فشل التجديد، توقف
            }
          } else if (e.toString().contains('JWT') && retryCount < maxRetries) {
            debugPrint('⚠️ مشكلة في JWT، انتظار وإعادة محاولة...');
            await Future.delayed(Duration(milliseconds: 1000 * retryCount));
          } else {
            // خطأ آخر، توقف
            debugPrint('خطأ غير قابل للإصلاح: ${e.toString()}');
            break;
          }
        }
      }

      // إذا فشلت جميع المحاولات
      if (!insertSuccess) {
        throw lastError ??
            Exception('فشل في إنشاء الحساب بعد $maxRetries محاولات');
      }

      // حفظ نسخة محلية للكاش
      await _cacheAccountData(userId, accountData);

      return accountData;
    } catch (e) {
      debugPrint('خطأ في إنشاء الحساب: $e');
      rethrow;
    }
  }

  /// التحقق من ربط الجهاز بالحساب
  static Future<bool> isDeviceLinked(String userId, String deviceId) async {
    try {
      final response = await Supabase.instance.client
          .from('user_devices')
          .select('id')
          .eq('user_id', userId)
          .eq('device_id', deviceId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      debugPrint('خطأ في التحقق من ربط الجهاز: $e');
      return false; // في حالة الخطأ، نسمح بالمتابعة
    }
  }

  /// التحقق من وجود حساب آخر في نفس الجهاز
  static Future<String?> getExistingAccountForDevice(String deviceId) async {
    try {
      debugPrint('🔍 [DEVICE_CHECK] فحص وجود حساب آخر للجهاز: $deviceId');

      // فحص مع تجاهل RLS للحصول على جميع السجلات
      final response = await Supabase.instance.client.rpc(
        'check_device_exists',
        params: {'device_id_param': deviceId},
      );

      if (response != null && response is List && response.isNotEmpty) {
        final existingUserId = response[0]['user_id'] as String;
        debugPrint('⚠️ [DEVICE_CHECK] وجد حساب موجود للجهاز: $existingUserId');
        return existingUserId;
      }

      // فحص احتياطي بالطريقة العادية
      final fallbackResponse = await Supabase.instance.client
          .from('user_devices')
          .select('user_id, created_at')
          .eq('device_id', deviceId)
          .maybeSingle();

      if (fallbackResponse != null) {
        final existingUserId = fallbackResponse['user_id'] as String;
        debugPrint(
          '⚠️ [DEVICE_CHECK] وجد حساب موجود (فحص احتياطي): $existingUserId',
        );

        // فحص إذا كان الحساب الموجود ما زال نشط
        final isAccountActive = await _isAccountStillActive(existingUserId);
        if (!isAccountActive) {
          debugPrint(
            '🔄 [DEVICE_CHECK] الحساب الموجود غير نشط، سيتم حذف ربط الجهاز',
          );
          await _unlinkDevice(deviceId);
          return null; // السماح بإنشاء حساب جديد
        }

        return existingUserId;
      }

      debugPrint('✅ [DEVICE_CHECK] لا يوجد حساب آخر للجهاز');
      return null;
    } catch (e) {
      debugPrint('❌ [DEVICE_CHECK] خطأ في فحص الجهاز: $e');

      // في حالة الخطأ، نمنع إنشاء الحساب للأمان
      throw Exception('خطأ في فحص الجهاز. يرجى المحاولة لاحقاً.');
    }
  }

  /// فحص إذا كان الحساب ما زال نشط
  static Future<bool> _isAccountStillActive(String userId) async {
    try {
      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      debugPrint('❌ [ACCOUNT_CHECK] خطأ في فحص نشاط الحساب: $e');
      return true; // في حالة الخطأ، نفترض أن الحساب نشط
    }
  }

  /// إلغاء ربط الجهاز
  static Future<void> _unlinkDevice(String deviceId) async {
    try {
      await Supabase.instance.client
          .from('user_devices')
          .delete()
          .eq('device_id', deviceId);

      debugPrint('✅ [UNLINK] تم إلغاء ربط الجهاز: $deviceId');
    } catch (e) {
      debugPrint('❌ [UNLINK] خطأ في إلغاء ربط الجهاز: $e');
    }
  }

  /// ربط الجهاز بالحساب
  static Future<void> linkDevice(
    String userId,
    String deviceId, {
    String? deviceName,
  }) async {
    try {
      debugPrint(
        '🔍 [LINK_DEVICE] محاولة ربط الجهاز: $deviceId بالمستخدم: $userId',
      );

      // التحقق من وجود حساب آخر في نفس الجهاز
      final existingUserId = await getExistingAccountForDevice(deviceId);
      if (existingUserId != null && existingUserId != userId) {
        throw Exception(
          'يوجد حساب آخر مرتبط بهذا الجهاز. لا يمكن إنشاء أكثر من حساب واحد في نفس الجهاز.',
        );
      }

      final deviceData = {
        'user_id': userId,
        'device_id': deviceId,
        'device_name': deviceName ?? 'جهاز غير معروف',
        'device_type': Platform.isAndroid ? 'Android' : 'iOS',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await Supabase.instance.client.from('user_devices').insert(deviceData);

      debugPrint('✅ [LINK_DEVICE] تم ربط الجهاز بالحساب: $deviceId');
    } catch (e) {
      debugPrint('❌ [LINK_DEVICE] خطأ في ربط الجهاز: $e');
      if (e.toString().contains('duplicate key value')) {
        debugPrint('⚠️ [LINK_DEVICE] الجهاز مربوط مسبقاً بنفس الحساب');
        return; // الجهاز مربوط مسبقاً بنفس الحساب، لا مشكلة
      }
      rethrow;
    }
  }

  // جلب بيانات الحساب من Supabase
  static Future<Map<String, dynamic>?> getAccountData(
    String userId, {
    bool useCache = true,
  }) async {
    try {
      // محاولة استخدام الكاش أولاً
      if (useCache) {
        final cachedData = await _getCachedAccountData(userId);
        if (cachedData != null) {
          debugPrint('تم جلب بيانات الحساب من الكاش');
          return cachedData;
        }
      }

      // جلب البيانات من Supabase
      try {
        final response = await Supabase.instance.client
            .from('user_accounts')
            .select('*')
            .eq('user_id', userId)
            .maybeSingle();

        if (response != null) {
          debugPrint('تم جلب بيانات الحساب من Supabase');

          // حفظ في الكاش
          await _cacheAccountData(userId, response);

          return response;
        } else {
          debugPrint('لا توجد بيانات حساب في Supabase للمستخدم: $userId');
          return null;
        }
      } catch (e) {
        debugPrint('خطأ في جلب البيانات من Supabase: $e');
        if (e.toString().contains('relation "user_accounts" does not exist')) {
          debugPrint('جدول user_accounts غير موجود في Supabase');
        }
        rethrow;
      }
    } catch (e) {
      debugPrint('خطأ في جلب بيانات الحساب: $e');

      // في حالة الخطأ، محاولة استخدام الكاش
      final cachedData = await _getCachedAccountData(userId);
      if (cachedData != null) {
        debugPrint('تم استخدام بيانات الكاش بسبب خطأ في الشبكة');
        return cachedData;
      }

      return null;
    }
  }

  // تحديث بيانات الحساب في Supabase
  static Future<bool> updateAccountData(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final updateData = {
        ...updates,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await Supabase.instance.client
          .from('user_accounts')
          .update(updateData)
          .eq('user_id', userId);

      debugPrint('تم تحديث بيانات الحساب في Supabase');

      // تحديث الكاش
      final currentData = await _getCachedAccountData(userId) ?? {};
      await _cacheAccountData(userId, {...currentData, ...updateData});

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات الحساب: $e');
      return false;
    }
  }

  // تفعيل الحساب
  static Future<bool> activateAccount(
    String userId,
    String packageName,
    int daysToAdd,
  ) async {
    try {
      final now = DateTime.now();
      final expiryDate = now.add(Duration(days: daysToAdd));

      final updates = {
        'is_trial': false,
        'activation_millis': now.millisecondsSinceEpoch,
        'expiry_millis': expiryDate.millisecondsSinceEpoch,
        'active_package': packageName,
      };

      return await updateAccountData(userId, updates);
    } catch (e) {
      debugPrint('خطأ في تفعيل الحساب: $e');
      return false;
    }
  }

  // فحص انتهاء الفترة التجريبية
  static Future<bool> isTrialExpired(String userId) async {
    try {
      final accountData = await getAccountData(userId);
      if (accountData == null) return false;

      final expiryMillis = accountData['expiry_millis'] ?? 0;
      if (expiryMillis == 0) return false;

      final now = DateTime.now();
      final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryMillis);

      return now.isAfter(expiryDate);
    } catch (e) {
      debugPrint('خطأ في فحص انتهاء الفترة التجريبية: $e');
      return false;
    }
  }

  // حساب الأيام المتبقية
  static Future<int> getDaysLeft(String userId) async {
    try {
      final accountData = await getAccountData(userId);
      if (accountData == null) return 0;

      final expiryMillis = accountData['expiry_millis'] ?? 0;
      if (expiryMillis == 0) return 0;

      final now = DateTime.now();
      final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryMillis);

      final difference = expiryDate.difference(now);
      return difference.inDays.clamp(0, 999);
    } catch (e) {
      debugPrint('خطأ في حساب الأيام المتبقية: $e');
      return 0;
    }
  }

  // حفظ بيانات الحساب في الكاش المحلي (مبسط)
  static Future<void> _cacheAccountData(
    String userId,
    Map<String, dynamic> data,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ البيانات الأساسية فقط
      await prefs.setBool('cache_is_trial_$userId', data['is_trial'] ?? true);
      await prefs.setInt(
        'cache_expiry_millis_$userId',
        data['expiry_millis'] ?? 0,
      );
      await prefs.setInt(
        'cache_creation_millis_$userId',
        data['creation_millis'] ?? 0,
      );
      await prefs.setString(
        'cache_display_name_$userId',
        data['display_name'] ?? '',
      );
      await prefs.setInt(
        'cache_time_$userId',
        DateTime.now().millisecondsSinceEpoch,
      );

      debugPrint('تم حفظ بيانات الحساب في الكاش');
    } catch (e) {
      debugPrint('خطأ في حفظ الكاش: $e');
    }
  }

  // جلب بيانات الحساب من الكاش المحلي (مبسط)
  static Future<Map<String, dynamic>?> _getCachedAccountData(
    String userId,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheTime = prefs.getInt('cache_time_$userId') ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // فحص انتهاء صلاحية الكاش
      if (now - cacheTime > _cacheExpiry.inMilliseconds) {
        debugPrint('انتهت صلاحية الكاش');
        return null;
      }

      // جلب البيانات من الكاش
      final cachedData = {
        'is_trial': prefs.getBool('cache_is_trial_$userId') ?? true,
        'expiry_millis': prefs.getInt('cache_expiry_millis_$userId') ?? 0,
        'creation_millis': prefs.getInt('cache_creation_millis_$userId') ?? 0,
        'display_name': prefs.getString('cache_display_name_$userId') ?? '',
      };

      debugPrint('تم العثور على بيانات في الكاش');
      return cachedData;
    } catch (e) {
      debugPrint('خطأ في جلب الكاش: $e');
      return null;
    }
  }

  // مسح الكاش
  static Future<void> clearCache(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${_cachePrefix}account_$userId';
      final cacheTimeKey = '${_cachePrefix}time_$userId';

      await prefs.remove(cacheKey);
      await prefs.remove(cacheTimeKey);

      debugPrint('تم مسح كاش الحساب');
    } catch (e) {
      debugPrint('خطأ في مسح الكاش: $e');
    }
  }

  // مزامنة البيانات المحلية القديمة مع Supabase
  static Future<void> migrateLocalDataToSupabase(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // جلب البيانات المحلية القديمة
      final localData = {
        'is_trial': prefs.getBool('is_trial') ?? true,
        'expiry_millis': prefs.getInt('expiry_millis') ?? 0,
        'creation_millis': prefs.getInt('creation_millis') ?? 0,
        'activation_millis': prefs.getInt('activation_millis') ?? 0,
        'active_package': prefs.getString('actve_package') ?? '',
        'display_name': prefs.getString('user_display_name') ?? '',
      };

      // التحقق من وجود بيانات في Supabase
      final existingData = await getAccountData(userId, useCache: false);

      if (existingData == null) {
        // إنشاء حساب جديد بالبيانات المحلية
        await createAccount(
          userId,
          displayName: localData['display_name']?.toString(),
        );
        debugPrint('تم ترحيل البيانات المحلية إلى Supabase');
      } else {
        debugPrint('البيانات موجودة مسبقاً في Supabase');
      }
    } catch (e) {
      debugPrint('خطأ في ترحيل البيانات: $e');
    }
  }

  /// التحقق من وجود الحساب في Supabase (للاستعلام الدوري)
  static Future<bool> checkAccountExists(String userId) async {
    try {
      debugPrint('🔍 [ACCOUNT_CHECK] فحص وجود الحساب: $userId');

      // التحقق من صحة الجلسة أولاً
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null || currentUser.id != userId) {
        debugPrint(
          '❌ [ACCOUNT_CHECK] الجلسة غير صالحة أو معرف المستخدم غير متطابق',
        );
        return false;
      }

      // فحص وجود الحساب في جدول user_accounts
      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('user_id')
          .eq('user_id', userId)
          .maybeSingle();

      final exists = response != null;
      debugPrint(
        '🔍 [ACCOUNT_CHECK] نتيجة الفحص: ${exists ? "موجود" : "غير موجود"}',
      );

      return exists;
    } catch (e) {
      debugPrint('❌ [ACCOUNT_CHECK] خطأ في فحص وجود الحساب: $e');

      // في حالة خطأ الشبكة، نعتبر الحساب موجود لتجنب قطع الجلسة بسبب مشاكل الاتصال
      if (e.toString().contains('network') ||
          e.toString().contains('connection') ||
          e.toString().contains('timeout')) {
        debugPrint('⚠️ [ACCOUNT_CHECK] خطأ شبكة - الاحتفاظ بالجلسة');
        return true;
      }

      // للأخطاء الأخرى (مثل عدم وجود الحساب)، نعتبر الحساب غير موجود
      return false;
    }
  }

  /// إنهاء الجلسة وتنظيف البيانات المحلية
  static Future<void> signOutAndCleanup() async {
    try {
      debugPrint('🚪 [ACCOUNT_CHECK] بدء إنهاء الجلسة وتنظيف البيانات...');

      // إنهاء جلسة Supabase
      await Supabase.instance.client.auth.signOut();

      // تنظيف الكاش المحلي
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_cachePrefix));
      for (final key in keys) {
        await prefs.remove(key);
      }

      debugPrint('✅ [ACCOUNT_CHECK] تم إنهاء الجلسة وتنظيف البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ [ACCOUNT_CHECK] خطأ في إنهاء الجلسة: $e');
    }
  }
}
