// نموذج بيانات المشترك

class Subscriber {
  int? id;
  int? remoteId; // معرف المشترك من الموقع
  String name;
  double totalDebt;
  String user;
  double subscriptionPrice;
  String subscriptionType;
  DateTime startDate;
  DateTime endDate;
  String phone;
  String? notes;
  String? ip;
  String? secondaryIP;
  String? subscriptionId;
  double? buyPrice;
  String status;
  int contract;
  int onlineStatus;
  int? boardId;
  int isDeleted;
  String sourceType;

  Subscriber({
    this.id,
    this.remoteId,
    required this.name,
    required this.totalDebt,
    required this.user,
    required this.subscriptionPrice,
    required this.subscriptionType,
    required this.startDate,
    required this.endDate,
    required this.phone,
    this.notes,
    this.ip,
    this.secondaryIP,
    this.subscriptionId,
    this.buyPrice,
    this.status = '',
    this.contract = 0,
    this.onlineStatus = 0,
    this.boardId,
    this.isDeleted = 0,
    this.sourceType = 'sas',
  });

  factory Subscriber.fromMap(Map<String, dynamic> map) {
    return Subscriber(
      id: map['id'],
      remoteId: map['remoteId'],
      name: map['name'],
      totalDebt: map['totalDebt']?.toDouble() ?? 0.0,
      user: map['user'] ?? '',
      subscriptionPrice: map['subscriptionPrice']?.toDouble() ?? 0.0,
      subscriptionType: map['subscriptionType'] ?? '',
      startDate: _parseDateTime(map['startDate']),
      endDate: _parseDateTime(map['endDate']),
      phone: map['phone'],
      notes: map['notes'],
      ip: map['ip'],
      secondaryIP: map['secondary_ip'],
      subscriptionId: map['subscriptionId'],
      buyPrice: map['buyPrice'] != null && map['buyPrice'] is num
          ? (map['buyPrice'] as num).toDouble()
          : (map['buyPrice'] != null
                ? double.tryParse(map['buyPrice'].toString())
                : null),
      status: map['status'] ?? '',
      contract: map['contract'] ?? _calculateContract(map),
      onlineStatus: map['online_status'] ?? 0,
      boardId: map['boardId'],
      isDeleted: map['isDeleted'] ?? 0,
      sourceType: map['source_type'] ?? 'sas',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'remoteId': remoteId,
      'name': name,
      'totalDebt': totalDebt,
      'user': user,
      'subscriptionPrice': subscriptionPrice,
      'subscriptionType': subscriptionType,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'phone': phone,
      'notes': notes,
      'ip': ip,
      'secondary_ip': secondaryIP,
      'subscriptionId': subscriptionId,
      'buyPrice': buyPrice,
      'status': status,
      'contract': contract,
      'online_status': onlineStatus,
      'boardId': boardId,
      'isDeleted': isDeleted,
      'source_type': sourceType,
    };
  }

  Subscriber copyWith({
    int? id,
    int? remoteId,
    String? name,
    double? totalDebt,
    String? user,
    double? subscriptionPrice,
    String? subscriptionType,
    DateTime? startDate,
    DateTime? endDate,
    String? phone,
    String? notes,
    String? ip,
    String? secondaryIP,
    String? subscriptionId,
    double? buyPrice,
    String? status,
    int? contract,
    int? onlineStatus,
    int? isDeleted,
    int? boardId,
    String? sourceType,
  }) {
    return Subscriber(
      id: id ?? this.id,
      remoteId: remoteId ?? this.remoteId,
      name: name ?? this.name,
      totalDebt: totalDebt ?? this.totalDebt,
      user: user ?? this.user,
      subscriptionPrice: subscriptionPrice ?? this.subscriptionPrice,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      phone: phone ?? this.phone,
      notes: notes ?? this.notes,
      ip: ip ?? this.ip,
      secondaryIP: secondaryIP ?? this.secondaryIP,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      buyPrice: buyPrice ?? this.buyPrice,
      status: status ?? this.status,
      contract: contract ?? this.contract,
      onlineStatus: onlineStatus ?? this.onlineStatus,
      isDeleted: isDeleted ?? this.isDeleted,
      boardId: boardId ?? this.boardId,
      sourceType: sourceType ?? this.sourceType,
    );
  }

  // دوال مساعدة لحالة المشترك
  bool get isDeletedStatus => isDeleted == 1;
  bool get isActive => contract == 1;
  bool get isExpired => contract == 0;
  bool get isOnline => onlineStatus == 1;
  bool isNearExpire([int days = 3]) =>
      isActive && endDate.difference(DateTime.now()).inDays <= days;

  int get remainingDays => endDate.difference(DateTime.now()).inDays;

  // دوال مساعدة لنوع المشترك
  bool get isSasSubscriber => sourceType == 'sas';
  bool get isManualSubscriber => sourceType == 'manual';
  bool get isEarthlinkSubscriber => sourceType == 'earthlink';

  // يمكن تعديل/حذف المشترك اليدوي فقط
  bool get canEdit => isManualSubscriber;
  bool get canDelete => isManualSubscriber;

  // دالة مساعدة لحساب contract إذا لم يكن موجوداً
  static int _calculateContract(Map<String, dynamic> map) {
    try {
      final endDate = DateTime.parse(map['endDate'] ?? '');
      return endDate.isAfter(DateTime.now()) ? 1 : 0;
    } catch (_) {
      return 0;
    }
  }
}
