# Firebase Security Rules للنسخ الاحتياطي السحابي

## 🔐 **قواعد Firestore Security Rules:**

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد مجموعة المستخدمين
    match /users/{userId} {
      // السماح للمستخدم بقراءة وكتابة بياناته فقط
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // قواعد مجموعة النسخ الاحتياطية الفرعية
      match /backups/{backupId} {
        // السماح للمستخدم بإدارة نسخه الاحتياطية فقط
        allow read, write: if request.auth != null && request.auth.uid == userId;
        
        // التحقق من صحة البيانات عند الكتابة
        allow create: if request.auth != null 
                      && request.auth.uid == userId
                      && validateBackupData(request.resource.data);
        
        allow update: if request.auth != null 
                      && request.auth.uid == userId
                      && validateBackupData(request.resource.data);
      }
    }
  }
  
  // دالة التحقق من صحة بيانات النسخة الاحتياطية
  function validateBackupData(data) {
    return data.keys().hasAll(['fileName', 'storagePath', 'fileSize', 'timestamp', 'backupType'])
           && data.fileName is string
           && data.storagePath is string
           && data.fileSize is number
           && data.timestamp is number
           && data.backupType is string;
  }
}
```

## 🗄️ **قواعد Firebase Storage Security Rules:**

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // قواعد مجلد النسخ الاحتياطية للمستخدمين
    match /users/{userId}/backups/{fileName} {
      // السماح للمستخدم برفع وتحميل نسخه الاحتياطية فقط
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // التحقق من نوع الملف عند الرفع
      allow create: if request.auth != null 
                    && request.auth.uid == userId
                    && isValidBackupFile(fileName);
      
      // السماح بالحذف للمستخدم فقط
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // منع الوصول لأي ملفات أخرى
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
  
  // دالة التحقق من صحة ملف النسخة الاحتياطية
  function isValidBackupFile(fileName) {
    return fileName.matches('.*\\.json$') 
           && fileName.size() < 100
           && fileName.matches('itower_backup_[0-9]+\\.json');
  }
}
```

## 📋 **شرح القواعد:**

### **Firestore Rules:**
1. **حماية البيانات**: كل مستخدم يمكنه الوصول لبياناته فقط
2. **التحقق من الهوية**: `request.auth.uid == userId`
3. **التحقق من البيانات**: `validateBackupData()` للتأكد من صحة البيانات
4. **مجلدات منفصلة**: `users/{userId}/backups/{backupId}`

### **Storage Rules:**
1. **حماية الملفات**: كل مستخدم يمكنه الوصول لملفاته فقط
2. **التحقق من نوع الملف**: فقط ملفات `.json`
3. **التحقق من اسم الملف**: يجب أن يتبع النمط المحدد
4. **حد حجم الاسم**: أقل من 100 حرف

## 🚀 **كيفية تطبيق القواعد:**

### **1. Firestore Rules:**
```bash
# في Firebase Console
1. اذهب إلى Firestore Database
2. اختر Rules
3. انسخ والصق قواعد Firestore
4. اضغط Publish
```

### **2. Storage Rules:**
```bash
# في Firebase Console
1. اذهب إلى Storage
2. اختر Rules
3. انسخ والصق قواعد Storage
4. اضغط Publish
```

## 🔍 **اختبار القواعد:**

### **Firestore Simulator:**
```javascript
// اختبار قراءة بيانات المستخدم
match /users/ZLaMKBSQEnY9aHdoy9PHR0Skct93 {
  // يجب أن ينجح للمستخدم نفسه
  allow read: if request.auth.uid == 'ZLaMKBSQEnY9aHdoy9PHR0Skct93';
}

// اختبار النسخ الاحتياطية
match /users/ZLaMKBSQEnY9aHdoy9PHR0Skct93/backups/backup_1753215941259 {
  // يجب أن ينجح للمستخدم نفسه فقط
  allow read, write: if request.auth.uid == 'ZLaMKBSQEnY9aHdoy9PHR0Skct93';
}
```

### **Storage Simulator:**
```javascript
// اختبار رفع ملف
match /users/ZLaMKBSQEnY9aHdoy9PHR0Skct93/backups/itower_backup_1753215941259.json {
  // يجب أن ينجح للمستخدم نفسه فقط
  allow create: if request.auth.uid == 'ZLaMKBSQEnY9aHdoy9PHR0Skct93';
}
```

## ⚠️ **ملاحظات مهمة:**

1. **تأكد من تسجيل الدخول**: جميع العمليات تتطلب `request.auth != null`
2. **معرف المستخدم**: يجب أن يطابق `request.auth.uid == userId`
3. **أسماء الملفات**: يجب أن تتبع النمط المحدد
4. **أنواع البيانات**: التحقق من صحة أنواع البيانات المرسلة

هذه القواعد تضمن أن كل مستخدم يمكنه الوصول لنسخه الاحتياطية فقط وتمنع أي وصول غير مصرح به.
